import asyncio
import time
from pathlib import Path
from typing import Protocol

import cv2 as cv
from heliovision.camera.base import Fr<PERSON>, FrameQueue, OffThreadAsyncFrameQueue
from loguru import logger


class ProfilerProtocol(Protocol):
    async def start_run(self, *args, **kwargs) -> FrameQueue: ...

    def update_settings(self, *args, **kwargs) -> None: ...

    def trigger(self) -> None: ...

    def stop_run(self) -> asyncio.Task[None]: ...


class MockProfiler(ProfilerProtocol):
    def __init__(self, data_folder: Path, delay: float = 0.0) -> None:
        self._frame_queue = OffThreadAsyncFrameQueue()
        self._data_folder = data_folder
        self._files = sorted(
            [f for f in data_folder.iterdir() if f.is_file() and f.suffix == '.png']
        )
        self._delay = delay
        self._counter = 0

    async def start_run(self, *args, **kwargs) -> FrameQueue:
        return self._frame_queue

    def update_settings(self, *args, **kwargs) -> None:
        pass

    def trigger(self) -> None:
        if not self._files:
            raise RuntimeError('No more files to process')

        file_path = self._files[self._counter]
        img = cv.imread(str(file_path))
        if img is None:
            raise RuntimeError(f'Failed to read image from {file_path}')
        frame = Frame()
        frame.image = img
        time.sleep(self._delay)
        self._frame_queue.put(frame)
        self._counter += 1
        if self._counter == len(self._files):
            self._counter = 0


async def update_profiler_settings(
    profiler: ProfilerProtocol,
    z_range_center: float,
    nr_of_profiles: int,
) -> FrameQueue | None:
    """Update the profiler settings with the given diameter."""
    try:
        profiler.update_settings(
            z_range_center=z_range_center,
            nr_of_profiles=nr_of_profiles,
        )
        logger.debug(
            f'Profiler settings updated: z_range_center={z_range_center}, nr_of_profiles={nr_of_profiles}'
        )
        await asyncio.sleep(1)
        frame_queue = await profiler.start_run(
            y_count=15000, y_pitch=0.1, use_external_batch_start=True
        )
        return frame_queue
    except Exception as e:
        logger.error(f'Failed to update profiler settings: {e}')
        raise

import unittest
from datetime import datetime

from dateutil.relativedelta import relativedelta
from pydantic import ValidationError

from src.protocols import InputDataExcelTree


class TestInputDataExcelTree(unittest.TestCase):
    def test_boolean_validation(self):
        """Test that non-boolean values raise a ValidationError."""
        with self.assertRaises(ValidationError):
            InputDataExcelTree(drew='not_a_boolean')

    def test_from_list_parsing(self):
        """Test parsing input from a list of strings."""
        input_data = ['drew', 'pi keur', 'e keur 1975-05-10', 'keurdatum 2018-07-15']

        result = InputDataExcelTree.from_list(input_data)

        self.assertTrue(result.drew)
        self.assertTrue(result.pi_keur)
        self.assertFalse(result.hexagon)
        self.assertFalse(result.epsilon)
        self.assertFalse(result.stoomwezen)
        self.assertFalse(result.wanddikte_treksterkte)
        self.assertFalse(result.pi_symbol)
        self.assertTrue(result.e_keur_before_1978)
        self.assertTrue(result.keurdatum_expired)

    def test_date_extraction_valid(self):
        """Test correct extraction of dates from string lists."""
        input_list = [
            'some text',
            'e keur 1980-01-01',
            'keurdatum 2015-12-25',
            'random entry without date',
            '1969-01-14',
        ]

        extracted_date = InputDataExcelTree.extract_date(input_list, 'e keur')
        self.assertEqual(extracted_date, datetime(1980, 1, 1))

        extracted_date = InputDataExcelTree.extract_date(input_list, 'keurdatum')
        self.assertEqual(extracted_date, datetime(2015, 12, 25))

    def test_date_extraction_invalid(self):
        """Test that missing dates raise a ValueError."""
        input_list = ['some text', 'random entry without date']

        with self.assertRaises(ValueError):
            InputDataExcelTree.extract_date(input_list, 'e keur')

        with self.assertRaises(ValueError):
            InputDataExcelTree.extract_date(input_list, 'keurdatum')

    def test_date_extraction_invalid_from_list(self):
        """Test that missing dates raise a ValueError when parsing from a list."""
        input_list = ['some text', 'random entry without date']

        with self.assertRaises(ValueError):
            InputDataExcelTree.from_list(input_list)

    def test_from_list_with_expired_keur_data(self):
        """Test parsing input where all the keur data is expired."""
        input_data = [
            'keurdatum 2010-05-10',
            'e keur 1975-05-10',
        ]

        result = InputDataExcelTree.from_list(input_data)

        self.assertTrue(result.keurdatum_expired)
        self.assertTrue(result.e_keur_before_1978)

    def test_from_list_with_non_expired_keur_data(self):
        """Test parsing input where all the keur data is not expired."""
        input_data = [
            'keurdatum 2024-05-10',
            'e keur 2010-05-10',
        ]

        result = InputDataExcelTree.from_list(input_data)

        self.assertFalse(result.keurdatum_expired)
        self.assertFalse(result.e_keur_before_1978)

    def test_from_list_edge_case_boundary(self):
        """Test edge case where keurdatum is exactly 4.5 years ago (should not be expired)."""
        today = datetime.today()
        keurdatum_str = (today - relativedelta(years=4, months=6)).strftime('%Y-%m-%d')

        input_data = [f'keurdatum {keurdatum_str}', 'e keur 2010-05-10']
        result = InputDataExcelTree.from_list(input_data)

        self.assertFalse(result.keurdatum_expired)

    def test_from_list_non_boolean_fields(self):
        """Test that non-boolean fields are still correctly interpreted."""
        input_data = [
            'πpi keur',
            'drew',
            'wanddikte',
            'epsilon',
            'stoomwezen',
            'hexagon',
            'Hey Glenn',
            'OCR GO BRRRR',
            'E keur 1975-05-10',
            'Keurdatum 2018-07-15',
        ]

        result = InputDataExcelTree.from_list(input_data)

        self.assertTrue(result.pi_symbol)
        self.assertTrue(result.wanddikte_treksterkte)
        self.assertTrue(result.epsilon)
        self.assertTrue(result.stoomwezen)
        self.assertTrue(result.hexagon)


if __name__ == '__main__':
    unittest.main()

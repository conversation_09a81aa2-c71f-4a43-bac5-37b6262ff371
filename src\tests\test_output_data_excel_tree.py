import unittest
from datetime import datetime

from src.protocols import (
    ApprovalType,
    EngravingCode,
    EngravingType,
    InputDataExcelTree,
    OutputDataExcelTree,
    RejectionReason,
)


class TestOutputDataExcelTree(unittest.TestCase):
    def test_default_rejection_status(self):
        """Test that DEFAULT rejection_status prevents engraving generation."""
        output = OutputDataExcelTree()
        self.assertEqual(output.to_engraving_string(), '')

    def test_rejected_bottle_no_engraving(self):
        """Test that a rejected bottle does not generate an engraving string."""
        output = OutputDataExcelTree(
            rejection_status=RejectionReason.NIET_KEUREN, approved=False
        )
        self.assertEqual(output.to_engraving_string(), '')

        output = OutputDataExcelTree(
            rejection_status=RejectionReason.GEEN_KEUR_ENKEL_PAINT, approved=False
        )
        self.assertEqual(output.to_engraving_string(), '')

    def test_rejection_reason_property(self):
        """Test that the rejection_reason property returns the correct string."""
        output = OutputDataExcelTree(rejection_status=RejectionReason.NIET_KEUREN)
        self.assertEqual(output.rejection_reason, 'niet keuren')

        output = OutputDataExcelTree(rejection_status=RejectionReason.GEEN_KEUR_ENKEL_PAINT)
        self.assertEqual(output.rejection_reason, 'geen keur enkel paint')

    def test_approved_bottle_generates_engraving(self):
        """Test that an approved bottle generates a correct engraving string."""
        output = OutputDataExcelTree(
            rejection_status=RejectionReason.NO_REJECTION,
            approved=True,
            approval_type=ApprovalType.HT,
            engraving_type=EngravingType.PI,
            engraving_code=EngravingCode.PI_0029_BBNGB,
        )

        expected_date = datetime.today().strftime('%Y/%m')
        expected_string = f'PI π 0029 BB̂NGB {expected_date}'
        self.assertEqual(output.to_engraving_string(), expected_string)

    def test_drew_true_keurdatum_expired_pi_keur(self):
        """Test a scenario where drew=True and keurdatum is expired and "PI keur" is found."""
        input_data = InputDataExcelTree(drew=True, keurdatum_expired=True, pi_keur=True)
        output = OutputDataExcelTree.from_input_excel_data(input_data)
        self.assertEqual(output.rejection_status, RejectionReason.NO_REJECTION)
        self.assertTrue(output.approved)
        self.assertEqual(output.approval_type, ApprovalType.HT)
        self.assertEqual(output.engraving_type, EngravingType.PI)
        self.assertEqual(output.engraving_code, EngravingCode.PI_0029_BBNGB_ENCIRCLED)

    def test_drew_true_keurdatum_expired_no_pi_keur(self):
        """Test drew=True and keurdatum expired but no pi keur."""
        input_data = InputDataExcelTree(drew=True, keurdatum_expired=True, pi_keur=False)
        output = OutputDataExcelTree.from_input_excel_data(input_data)
        self.assertEqual(output.rejection_status, RejectionReason.NO_REJECTION)
        self.assertTrue(output.approved)
        self.assertEqual(output.approval_type, ApprovalType.HT)
        self.assertEqual(output.engraving_type, EngravingType.APRAGAZ_EXPORT)
        self.assertEqual(output.engraving_code, EngravingCode.A)

    def test_drew_true_keurdatum_not_expired(self):
        """Test drew=True but keurdatum is NOT expired (should be rejected)."""
        input_data = InputDataExcelTree(drew=True, keurdatum_expired=False)
        output = OutputDataExcelTree.from_input_excel_data(input_data)
        self.assertEqual(output.rejection_status, RejectionReason.GEEN_KEUR_ENKEL_PAINT)
        self.assertFalse(output.approved)

    def test_pi_symbol_and_treksterkte_e_keur_before_1978(self):
        """Test if pi_symbol and wanddikte_treksterkte are both present."""
        input_data = InputDataExcelTree(
            pi_symbol=True, wanddikte_treksterkte=True, e_keur_before_1978=True
        )
        output = OutputDataExcelTree.from_input_excel_data(input_data)
        self.assertEqual(output.rejection_status, RejectionReason.NO_REJECTION)
        self.assertTrue(output.approved)
        self.assertEqual(output.approval_type, ApprovalType.HT)
        self.assertEqual(output.engraving_type, EngravingType.PI)
        self.assertEqual(output.engraving_code, EngravingCode.UT_PI_0029_B_NGB)

    def test_pi_symbol_and_treksterkte_e_keur_after_1978(self):
        """Test if pi_symbol and wanddikte_treksterkte are both present."""
        input_data = InputDataExcelTree(
            pi_symbol=True, wanddikte_treksterkte=True, e_keur_before_1978=False
        )
        output = OutputDataExcelTree.from_input_excel_data(input_data)
        self.assertEqual(output.rejection_status, RejectionReason.NO_REJECTION)
        self.assertTrue(output.approved)
        self.assertEqual(output.approval_type, ApprovalType.UT)
        self.assertEqual(output.engraving_type, EngravingType.PI)
        self.assertEqual(output.engraving_code, EngravingCode.UT_PI_0029_B_NGB)

    def test_pi_symbol_only(self):
        """Test when only pi_symbol is present."""
        input_data = InputDataExcelTree(pi_symbol=True, wanddikte_treksterkte=False)
        output = OutputDataExcelTree.from_input_excel_data(input_data)
        self.assertEqual(output.rejection_status, RejectionReason.NO_REJECTION)
        self.assertTrue(output.approved)
        self.assertEqual(output.approval_type, ApprovalType.HT)
        self.assertEqual(output.engraving_type, EngravingType.PI)
        self.assertEqual(output.engraving_code, EngravingCode.PI_0029_BBNGB_ENCIRCLED)

    def test_wanddikte_treksterkte_without_pi_symbol_raises_error(self):
        """Test that having wanddikte_treksterkte without pi_symbol raises an error."""
        input_data = InputDataExcelTree(pi_symbol=False, wanddikte_treksterkte=True)
        with self.assertRaises(ValueError):
            OutputDataExcelTree.from_input_excel_data(input_data)

    def test_no_pi_symbol_no_treksterkte_stoomwezen_rejection(self):
        """Test that when no pi_symbol, no treksterkte and stoomwezen=True, the bottle is rejected."""
        input_data = InputDataExcelTree(
            pi_symbol=False, wanddikte_treksterkte=False, stoomwezen=True
        )
        output = OutputDataExcelTree.from_input_excel_data(input_data)
        self.assertEqual(output.rejection_status, RejectionReason.NIET_KEUREN)
        self.assertFalse(output.approved)

    def test_no_pi_symbol_no_treksterkte_no_stoomwezen_e_keur_before_1978(self):
        """Test that when no pi_symbol, no treksterkte, no stoomwezen and e_keur_before_1978, the bottle is approved."""
        input_data = InputDataExcelTree(
            pi_symbol=False,
            wanddikte_treksterkte=False,
            stoomwezen=False,
            e_keur_before_1978=True,
        )
        output = OutputDataExcelTree.from_input_excel_data(input_data)
        self.assertEqual(output.rejection_status, RejectionReason.NO_REJECTION)
        self.assertTrue(output.approved)
        self.assertEqual(output.approval_type, ApprovalType.HT)
        self.assertEqual(output.engraving_type, EngravingType.APRAGAZ_KEURING)
        self.assertEqual(output.engraving_code, EngravingCode.BB)

    def test_no_pi_symbol_no_treksterkte_no_stoomwezen_e_keur_after_1978_no_hexagon_or_episilon(
        self,
    ):
        """Test that when no pi_symbol, no treksterkte, no stoomwezen, not e_keur_before_1978 and no hexagon or epsilon, the bottle is approved."""
        input_data = InputDataExcelTree(
            pi_symbol=False,
            wanddikte_treksterkte=False,
            stoomwezen=False,
            e_keur_before_1978=False,
            hexagon=False,
            epsilon=False,
        )
        output = OutputDataExcelTree.from_input_excel_data(input_data)
        self.assertEqual(output.rejection_status, RejectionReason.NO_REJECTION)
        self.assertTrue(output.approved)
        self.assertEqual(output.approval_type, ApprovalType.HT)
        self.assertEqual(output.engraving_type, EngravingType.APRAGAZ_KEURING)
        self.assertEqual(output.engraving_code, EngravingCode.BB)

    def test_no_pi_symbol_no_treksterkte_no_stoomwezen_e_keur_after_1978_no_hexagon_but_episilon(
        self,
    ):
        """Test that when no pi_symbol, no treksterkte, no stoomwezen, not e_keur_before_1978 and no hexagon but epsilon=true, the bottle is approved."""
        input_data = InputDataExcelTree(
            pi_symbol=False,
            wanddikte_treksterkte=False,
            stoomwezen=False,
            e_keur_before_1978=False,
            hexagon=False,
            epsilon=True,
        )
        output = OutputDataExcelTree.from_input_excel_data(input_data)
        self.assertEqual(output.rejection_status, RejectionReason.NO_REJECTION)
        self.assertTrue(output.approved)
        self.assertEqual(output.approval_type, ApprovalType.HT)
        self.assertEqual(output.engraving_type, EngravingType.DEFAULT)
        self.assertEqual(output.engraving_code, EngravingCode.PI_0029_BBNGB)

    def test_no_pi_symbol_no_treksterkte_no_stoomwezen_e_keur_after_1978_hexagon_no_episilon(
        self,
    ):
        """Test that when no pi_symbol, no treksterkte, no stoomwezen, not e_keur_before_1978 and hexagon=true and no epsilon, the bottle is approved."""
        input_data = InputDataExcelTree(
            pi_symbol=False,
            wanddikte_treksterkte=False,
            stoomwezen=False,
            e_keur_before_1978=False,
            hexagon=True,
            epsilon=False,
        )
        output = OutputDataExcelTree.from_input_excel_data(input_data)
        self.assertEqual(output.rejection_status, RejectionReason.NO_REJECTION)
        self.assertTrue(output.approved)
        self.assertEqual(output.approval_type, ApprovalType.HT)
        self.assertEqual(output.engraving_type, EngravingType.DEFAULT)
        self.assertEqual(output.engraving_code, EngravingCode.PI_0029_BBNGB)

    def test_no_pi_symbol_no_treksterkte_no_stoomwezen_e_keur_after_1978_both_hexagon_and_epsilon(
        self,
    ):
        """Test that when no pi_symbol, no treksterkte, no stoomwezen, not e_keur_before_1978 and hexagon=true and epsilon=true, the bottle is approved."""
        input_data = InputDataExcelTree(
            pi_symbol=False,
            wanddikte_treksterkte=False,
            stoomwezen=False,
            e_keur_before_1978=False,
            hexagon=True,
            epsilon=True,
        )
        output = OutputDataExcelTree.from_input_excel_data(input_data)
        self.assertEqual(output.rejection_status, RejectionReason.NO_REJECTION)
        self.assertTrue(output.approved)
        self.assertEqual(output.approval_type, ApprovalType.HT)
        self.assertEqual(output.engraving_type, EngravingType.DEFAULT)
        self.assertEqual(output.engraving_code, EngravingCode.PI_0029_BBNGB)


if __name__ == '__main__':
    unittest.main()

import asyncio

import pytest
import pytest_asyncio
from asyncua import Node, Server
from asyncua.ua import <PERSON>V<PERSON><PERSON>, Variant, VariantType

from src.communication.botko_nodes import BotkoOPCUANode
from src.communication.opcua import OPCClient
from src.mock.mock_opc_server import generate_nodes_from_enum


@pytest_asyncio.fixture(scope='function', loop_scope='function')
async def mock_server():
    """Fixture to set up a mock OPC UA server."""
    server = Server()
    await server.init()
    server.set_endpoint('opc.tcp://localhost:4840/')
    uri = 'nippon_it'
    address_space = await server.register_namespace(uri)
    root = server.get_objects_node()

    # Add nodes to the server
    nodes = await generate_nodes_from_enum(root, address_space, BotkoOPCUANode)

    await server.start()

    await asyncio.sleep(1)

    # Start the server
    try:
        yield {
            'server': server,
            'nodes': nodes,
        }
        # yield server
    finally:
        await server.stop()


@pytest_asyncio.fixture(scope='function', loop_scope='function')
async def botko_client():
    """Fixture to set up the OPCClient."""

    client = OPCClient(
        server_url='opc.tcp://localhost:4840',
        node_descriptions=BotkoOPCUANode,
        namespace='nippon_it',
    )
    await client.initialize(BotkoOPCUANode.BOTKO_LIVE_BIT, BotkoOPCUANode.HV_LIVE_BIT)
    return client


@pytest.mark.parametrize(
    'node_name, expected_type, set_value',
    [
        (BotkoOPCUANode.BOTKO_FAULTED, VariantType.Boolean, True),
        (BotkoOPCUANode.BOTKO_ENGRAVING_BUSY, VariantType.Boolean, True),
        (BotkoOPCUANode.BOTKO_UT_HT, VariantType.String, 'UT'),
        (BotkoOPCUANode.BOTKO_DIAMETER, VariantType.Int32, 100),
        (BotkoOPCUANode.BOTKO_LENGTH, VariantType.Int32, 200),
        (BotkoOPCUANode.BOTKO_SCAN_REQUEST, VariantType.Boolean, True),
        (BotkoOPCUANode.HV_FAULTED, VariantType.Boolean, True),
        (BotkoOPCUANode.HV_SCAN_ACKNOWLEDGE, VariantType.Boolean, True),
        (BotkoOPCUANode.HV_SCAN_BUSY, VariantType.Boolean, True),
        (BotkoOPCUANode.HV_SCAN_FINISHED, VariantType.Boolean, True),
        (BotkoOPCUANode.HV_START_ROTATION_REQUEST, VariantType.Boolean, True),
        (BotkoOPCUANode.HV_ENGRAVING_LOCATION_READY, VariantType.Boolean, True),
        (BotkoOPCUANode.HV_PASSPORT_READY, VariantType.Boolean, True),
        (BotkoOPCUANode.HV_ENGRAVING_RADIUS, VariantType.Int32, 50),
        (BotkoOPCUANode.HV_ENGRAVING_HEIGHT, VariantType.Int32, 100),
        (BotkoOPCUANode.HV_ENGRAVING_ANGLE, VariantType.Int32, 90),
        (BotkoOPCUANode.HV_PASSPORT_OWNER_NAME, VariantType.String, 'Test Owner'),
        (BotkoOPCUANode.HV_PASSPORT_OWNER_CODE, VariantType.Int32, 12345),
        (BotkoOPCUANode.HV_PASSPORT_SERIAL_NUMBER, VariantType.String, 'SN123456'),
        (BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_NAME, VariantType.String, 'Test Manufacturer'),
        (BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_CODE, VariantType.Int32, 54321),
        (
            BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_SERIAL_NUMBER,
            VariantType.String,
            'MSN123456',
        ),
        (BotkoOPCUANode.HV_PASSPORT_DATE_MANUFACTURING, VariantType.String, '2023-01-01'),
        (BotkoOPCUANode.HV_PASSPORT_DATE_LAST_TEST, VariantType.String, '2023-06-01'),
        (BotkoOPCUANode.HV_PASSPORT_TEST_PRESSURE, VariantType.Float, 200.0),
        (BotkoOPCUANode.HV_PASSPORT_CAPACITY, VariantType.Float, 50.0),
        (BotkoOPCUANode.HV_PASSPORT_ORIGINAL_TARRA_WEIGHT, VariantType.Float, 10.0),
    ],
)
@pytest.mark.asyncio
async def test_botko_client_receive(
    mock_server, botko_client: OPCClient, node_name, expected_type, set_value
):
    """Test that OPCClient can read various nodes."""
    nodes = mock_server['nodes']
    server_node: Node = nodes[node_name.value.node_id]
    # Set the value on the server
    server_value = DataValue(Variant(set_value, expected_type))
    await server_node.set_value(server_value)
    value = await botko_client.receive(node_name)
    assert value == set_value, f'Expected {set_value}, but got {value}'


@pytest.mark.parametrize(
    'node_name, set_value',
    [
        (BotkoOPCUANode.BOTKO_FAULTED, True),
        (BotkoOPCUANode.BOTKO_ENGRAVING_BUSY, True),
        (BotkoOPCUANode.BOTKO_UT_HT, 'HT'),
        (BotkoOPCUANode.BOTKO_DIAMETER, 100),
        (BotkoOPCUANode.BOTKO_LENGTH, 200),
        (BotkoOPCUANode.BOTKO_SCAN_REQUEST, True),
        (BotkoOPCUANode.HV_FAULTED, True),
        (BotkoOPCUANode.HV_SCAN_ACKNOWLEDGE, True),
        (BotkoOPCUANode.HV_SCAN_BUSY, True),
        (BotkoOPCUANode.HV_SCAN_FINISHED, True),
        (BotkoOPCUANode.HV_START_ROTATION_REQUEST, True),
        (BotkoOPCUANode.HV_ENGRAVING_LOCATION_READY, True),
        (BotkoOPCUANode.HV_PASSPORT_READY, True),
        (BotkoOPCUANode.HV_ENGRAVING_RADIUS, 50),
        (BotkoOPCUANode.HV_ENGRAVING_HEIGHT, 100),
        (BotkoOPCUANode.HV_ENGRAVING_ANGLE, 90),
        (BotkoOPCUANode.HV_PASSPORT_OWNER_NAME, 'Test Owner'),
        (BotkoOPCUANode.HV_PASSPORT_OWNER_CODE, 12345),
        (BotkoOPCUANode.HV_PASSPORT_SERIAL_NUMBER, 'SN123456'),
        (BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_NAME, 'Test Manufacturer'),
        (BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_CODE, 54321),
        (
            BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_SERIAL_NUMBER,
            'MSN123456',
        ),
        (BotkoOPCUANode.HV_PASSPORT_DATE_MANUFACTURING, '2023-01-01'),
        (BotkoOPCUANode.HV_PASSPORT_DATE_LAST_TEST, '2023-06-01'),
        (BotkoOPCUANode.HV_PASSPORT_TEST_PRESSURE, 200.0),
        (BotkoOPCUANode.HV_PASSPORT_CAPACITY, 50.0),
        (BotkoOPCUANode.HV_PASSPORT_ORIGINAL_TARRA_WEIGHT, 10.0),
    ],
)
@pytest.mark.asyncio
async def test_botko_client_send(mock_server, botko_client: OPCClient, node_name, set_value):
    """Test that OPCClient can send various nodes."""
    nodes = mock_server['nodes']
    await botko_client.send(node_name, set_value)
    server_node: Node = nodes[node_name.value.node_id]
    value = await server_node.get_value()
    assert value == set_value, f'Expected {set_value}, but got {value}'

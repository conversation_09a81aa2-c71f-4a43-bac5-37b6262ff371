import cv2 as cv
import numpy as np
from loguru import logger


def fit_points_to_circle(points):
    """
    Fit points to a circle in 2D space using least squares.
    Parameters:
        points (np.ndarray): (N, 2) array of 2D points
    Returns:
        np.ndarray: (2,) center of the fitted circle
        float: radius of the fitted circle
    """

    A = np.c_[points, np.ones(points.shape[0])]
    b = np.sum(points**2, axis=1)
    C = np.linalg.lstsq(A, b, rcond=None)[0]
    center = np.array([C[0] / 2, C[1] / 2])
    radius = np.sqrt(C[2] + (C[0] ** 2 + C[1] ** 2) / 4)
    return center, radius


def get_radius_and_theta_at_height(
    points: np.ndarray,
    normals: np.ndarray,
    height: float,
) -> tuple[float, float]:
    """
    Calculate the radius of the point cloud and the angle with the z-axis at a given height.

    Args:
        points: (N, 3) array of 3D points
        normals: (N, 3) array of normal vectors corresponding to the points
        height: height at which to calculate the radius

    Returns:
        radius: radius at the specified height
        theta: angle with the z-axis at the specified height
    """
    # Filter points at the specified height
    mask = np.isclose(points[:, 2], height, atol=0.004)
    filtered_points = points[mask]
    filtered_normals = normals[mask]
    thetas = np.degrees(np.arccos(filtered_normals[:, 2]))
    thetas = [theta if theta <= 90 else 180 - theta for theta in thetas]
    theta = float(np.median(thetas))
    theta -= 5
    if theta > 75:  # Safety limit for theta
        theta = 75

    if filtered_points.shape[0] == 0:
        logger.warning('No points')
        return 0.0, 75.0

    # Fit the points to a circle
    _, radius1 = fit_points_to_circle(filtered_points[:, :2])
    _, radius2 = cv.minEnclosingCircle(filtered_points[:, :2].astype(np.float32))
    logger.trace(f'Radius fit: {radius1}')
    logger.trace(f'Radius minEnclosing: {radius2}')
    radius = (radius1 + radius2) / 2  # Average the two radius estimates

    return radius, theta

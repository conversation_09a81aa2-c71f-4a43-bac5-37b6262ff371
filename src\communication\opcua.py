import asyncio
from typing import Any, Optional, Protocol, Type

from heliovision.communication.opcua import OPCUAClient
from loguru import logger

from src.communication.opcua_nodes import OPCUANodes
from src.detections.free_space import EmptyRectArea
from src.product.passport import Passport


class OPCClientProtocol(Protocol):
    async def initialize(
        self,
        heartbeat_send_node: OPCUANodes,
        heartbeat_monitor_node: OPCUANodes,
        heartbeat_send_period: float = 2.0,
        heartbeat_monitor_timeout: float = 2.0,
    ) -> None: ...

    async def send(self, node: OPCUANodes, value: Any) -> None: ...

    async def receive(self, node: OPCUANodes) -> Any: ...

    async def await_for_value(self, node: OPCUANodes, value: Any) -> Any: ...

    async def await_for_nonzero_value(self, node: OPCUANodes, period: float = 0.5) -> Any: ...


class OPCClient:
    def __init__(
        self,
        server_url: str,
        node_descriptions: Type[OPCUANodes],
        namespace: Optional[str] = None,
    ) -> None:
        self._client = OPCUAClient(server_url, namespace_name=namespace)
        self._node_descriptions = node_descriptions
        self._nodes = {}
        self._heartbeat_task: Optional[asyncio.Task] = None
        self._monitor_heartbeat_task: Optional[asyncio.Task] = None

    async def initialize(
        self,
        heartbeat_send_node: OPCUANodes,
        heartbeat_monitor_node: OPCUANodes,
        heartbeat_send_period: float = 2.0,
        heartbeat_monitor_timeout: float = 2.0,
    ) -> None:
        """Initialize the OPC UA client."""
        await self._client.connect()
        self._nodes = {
            name: await self._client.get_node(node.node_id, node.data_type)
            for name, node in self._node_descriptions.get_all_nodes().items()
        }
        self._start_heartbeat_task(heartbeat_send_node, heartbeat_send_period)
        # self._start_monitor_heartbeat_task(heartbeat_monitor_node, heartbeat_monitor_timeout)
        logger.debug('OPC UA client initialized and connected to server')

    def _start_heartbeat_task(self, node: OPCUANodes, period: float = 2.0) -> None:
        """Start a heartbeat task for the given node."""
        if self._heartbeat_task:
            logger.warning('Heartbeat task already running.')
            return
        if node.name not in self._nodes:
            raise KeyError(f'Node {node.name} not found in the client')
        self._heartbeat_task = asyncio.create_task(self._heartbeat(node, period))
        logger.debug(f'Started heartbeat task for node {node.name} with period {period} seconds')

    async def _heartbeat(self, node: OPCUANodes, period: float = 2.0) -> None:
        """Periodically send a heartbeat signal to the node."""
        node_name = node.name
        value = False
        if node_name not in self._nodes:
            raise KeyError(f'Node {node_name} not found in the client')
        while True:
            try:
                value = not value  # Toggle the heartbeat value
                logger.trace(f'Setting heartbeat to {value}')
                await self._nodes[node_name].set(value)
                await asyncio.sleep(period)
            except Exception as e:
                logger.exception(f'Error while sending heartbeat to node {node_name}: {e}')
                await self._client._is_connected.wait()  # Wait for reconnection

    def _start_monitor_heartbeat_task(self, node: OPCUANodes, timeout: float = 2.0) -> None:
        """Monitor the heartbeat of the given node."""
        if self._monitor_heartbeat_task:
            logger.warning('Heartbeat monitor task already running.')
            return
        if node.name not in self._nodes:
            raise KeyError(f'Node {node.name} not found in the client')
        self._monitor_heartbeat_task = asyncio.create_task(
            self._monitor_heartbeat(node, timeout)
        )
        logger.debug(
            f'Started heartbeat monitor task for node {node.name} with timeout {timeout} seconds'
        )

    async def _monitor_heartbeat(self, node: OPCUANodes, timeout: float = 2.0) -> None:
        """Monitor the heartbeat of the node and log its status."""
        node_name = node.name
        if node_name not in self._nodes:
            raise KeyError(f'Node {node_name} not found in the client')
        value = await self._nodes[node_name].get()
        while True:
            try:
                await asyncio.wait_for(self.await_for_value(node, not value), timeout=timeout)
                value = not value  # Toggle the heartbeat value
                logger.trace(f'Heartbeat for node {node_name} is alive: {value}')
            except asyncio.TimeoutError:
                raise TimeoutError(
                    f'Heartbeat for node {node_name} did not respond within {timeout} seconds'
                )
            except Exception as e:
                logger.exception(f'Error while monitoring heartbeat for node {node_name}: {e}')
                break

    async def send(self, node: OPCUANodes, value: Any) -> None:
        """Send a value to a node."""
        node_name = node.name
        if node_name not in self._nodes:
            raise KeyError(f'Node {node_name} not found in the client')
        required_type = self._node_descriptions.get_node(node_name).data_type
        if isinstance(value, list):
            if not all(isinstance(v, required_type) for v in value):
                raise ValueError(f'Value type mismatch for node {node_name}')
        elif not isinstance(value, required_type):
            raise ValueError(f'Value type mismatch for node {node_name}')
        try:
            await self._nodes[node_name].set(value)
        except Exception as e:
            logger.exception(f'Error while setting value for node {node_name}: {e}')

    async def receive(self, node: OPCUANodes) -> Any:
        """Receive a value from a node."""
        node_name = node.name
        try:
            if node_name not in self._nodes:
                raise KeyError(f'Node {node_name} not found in the client')
        except KeyError:
            raise ValueError(f'Node {node_name} not found in the client')
        return await self._nodes[node_name].get()

    async def await_for_value(self, node: OPCUANodes, value: Any) -> Any:
        node_name = node.name
        try:
            if node_name not in self._nodes:
                raise KeyError(f'Node {node_name} not found in the client')
        except KeyError:
            raise ValueError(f'Node {node_name} not found in the client')
        return await self._nodes[node_name].wait_for_value(value)

    async def await_for_nonzero_value(self, node: OPCUANodes, period: float = 0.5) -> Any:
        node_name = node.name
        try:
            if node_name not in self._nodes:
                raise KeyError(f'Node {node_name} not found in the client')
        except KeyError:
            raise ValueError(f'Node {node_name} not found in the client')
        while True:
            try:
                res = await self._nodes[node_name].get()
                if res != 0:
                    return res
                else:
                    await asyncio.sleep(period)
            except Exception:
                logger.exception('Error while waiting for non-zero value')
                raise


async def monitor_faulted(
    opc_client: OPCClientProtocol, botko_faulted_node: OPCUANodes, cancel_event: asyncio.Event
) -> None:
    """Monitor provided faulted node and set the cancel event when it is True."""
    logger.debug(f'Starting to monitor node {botko_faulted_node.name}')
    while not cancel_event.is_set():
        try:
            faulted = await opc_client.receive(botko_faulted_node)
            if faulted:
                logger.warning('Faulted node is True, setting cancel event.')
                cancel_event.set()
                break
        except Exception as e:
            logger.error(f'Error while monitoring faulted node: {e}')
            break
        await asyncio.sleep(0.5)  # Adjust the sleep time as needed


async def perform_free_space_sequence(
    opc_client: OPCClientProtocol,
    engraving_radius_node: OPCUANodes,
    engraving_angle_node: OPCUANodes,
    engraving_height_node: OPCUANodes,
    scan_finished_node: OPCUANodes,
    rotation_request_node: OPCUANodes,
    engraving_location_ready_node: OPCUANodes,
    free_space: list[EmptyRectArea],
) -> None:
    time_to_turn = 0
    if len(free_space) == 0:
        logger.warning('No free spaces detected in the frame.')
        r = 666
        theta = 666
        z = 666
    else:
        logger.debug(f'Sending free space {free_space[0]}')
        r = free_space[0].r
        theta = free_space[0].theta
        z = free_space[0].z
        # time_to_turn = free_space[0].time_to_turn #TODO
    await opc_client.send(engraving_radius_node, r)
    await opc_client.send(engraving_angle_node, theta)
    await opc_client.send(engraving_height_node, z)
    await opc_client.send(scan_finished_node, True)
    if time_to_turn > 0:
        await opc_client.send(rotation_request_node, True)
        logger.debug('Sent rotation request')
        await asyncio.to_thread(input, 'Press Enter to continue...')
        await opc_client.send(rotation_request_node, False)
        logger.debug('Rotation done')
        await opc_client.send(engraving_location_ready_node, True)


async def send_passport(
    opc_client: OPCClientProtocol,
    owner_name_node: OPCUANodes,
    owner_code_node: OPCUANodes,
    serial_number_node: OPCUANodes,
    manufacturer_name_node: OPCUANodes,
    manufacturer_code_node: OPCUANodes,
    manufacturer_serial_number_node: OPCUANodes,
    manufacturing_date_node: OPCUANodes,
    last_test_date_node: OPCUANodes,
    test_pressure_node: OPCUANodes,
    capacity_node: OPCUANodes,
    original_tare_weight_node: OPCUANodes,
    passport_ready_node: OPCUANodes,
    passport: Passport,
) -> None:
    await opc_client.send(owner_name_node, passport.owner_name)
    await opc_client.send(owner_code_node, passport.owner_code)
    await opc_client.send(serial_number_node, passport.serial_number)
    await opc_client.send(manufacturer_name_node, passport.manufacturer_name)
    await opc_client.send(manufacturer_code_node, passport.manufacturer_code)
    await opc_client.send(
        manufacturer_serial_number_node,
        passport.manufacturer_serial_number,
    )
    await opc_client.send(manufacturing_date_node, passport.manufacturing_date)
    await opc_client.send(last_test_date_node, passport.last_test_date)
    await opc_client.send(test_pressure_node, passport.test_pressure)
    await opc_client.send(capacity_node, passport.capacity)
    await opc_client.send(original_tare_weight_node, passport.original_tare_weight)
    await opc_client.send(passport_ready_node, True)
    logger.debug(f'Sent passport: {passport}')

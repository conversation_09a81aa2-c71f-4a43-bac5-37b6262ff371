import asyncio
from datetime import datetime
from typing import cast

import numpy as np
from heliovision.camera.base import FrameQueue
from heliovision.camera.keyence.profiler import Profiler<PERSON>rame
from loguru import logger

from src.analyzer import AnalyzerProtocol
from src.communication.botko_nodes import BotkoOPCUANode
from src.communication.opcua import OPCClientProtocol, perform_free_space_sequence, send_passport
from src.communication.profiler import ProfilerProtocol
from src.gui.gui import GUIProtocol


async def initialize(
    profiler: ProfilerProtocol, opc_client: OPCClientProtocol, gui: GUIProtocol
) -> FrameQueue:
    logger.info('Initializing the main flow...')
    try:
        frame_queue = await profiler.start_run(
            y_count=15000, y_pitch=0.1, use_external_batch_start=True
        )
        logger.info('Profiler started successfully.')
        await opc_client.initialize(
            BotkoOPCUANode.HV_LIVE_BIT,
            BotkoOPCUANode.BOTKO_LIVE_BIT,
            heartbeat_send_period=2.0,
            heartbeat_monitor_timeout=5.0,
        )
        logger.info('OPC client initialized successfully.')
        await gui.initialize()
        logger.info('GUI initialized successfully.')
        return frame_queue
    except Exception as e:
        logger.error(f'Failed to properly initialize: {e}')
        raise


async def flow_iteration(
    profiler: ProfilerProtocol,
    frame_queue: FrameQueue,
    opc_client: OPCClientProtocol,
    analyzer: AnalyzerProtocol,
    gui: GUIProtocol,
) -> None:
    logger.debug('Flow iteration started.')
    try:
        logger.debug('Resetting before start')
        await opc_client.send(BotkoOPCUANode.HV_FAULTED, False)
        await opc_client.send(BotkoOPCUANode.HV_SCAN_BUSY, False)
        await opc_client.send(BotkoOPCUANode.HV_SCAN_ACKNOWLEDGE, False)
        await opc_client.send(BotkoOPCUANode.HV_SCAN_FINISHED, False)
        await opc_client.send(BotkoOPCUANode.HV_ENGRAVING_LOCATION_READY, False)
        await opc_client.send(BotkoOPCUANode.HV_PASSPORT_READY, False)
        await opc_client.send(BotkoOPCUANode.HV_ENGRAVING_RADIUS, 0)
        await opc_client.send(BotkoOPCUANode.HV_ENGRAVING_ANGLE, 0)
        await opc_client.send(BotkoOPCUANode.HV_ENGRAVING_HEIGHT, 0)
        logger.debug('Awaiting length and diameter and UT/HT')
        length = await opc_client.await_for_nonzero_value(BotkoOPCUANode.BOTKO_LENGTH)
        diameter = await opc_client.await_for_nonzero_value(BotkoOPCUANode.BOTKO_DIAMETER)
        ut_ht = await opc_client.receive(BotkoOPCUANode.BOTKO_UT_HT)
        await gui.clear()
        logger.debug(f'Got length {length}, diameter {diameter}, UT/HT: {ut_ht}')
        profiler.update_settings(length=length, diameter=diameter)
        logger.debug('Awaiting scan request')
        await opc_client.await_for_value(BotkoOPCUANode.BOTKO_SCAN_REQUEST, True)
        logger.debug('Got scan request')
        await opc_client.send(BotkoOPCUANode.HV_SCAN_ACKNOWLEDGE, True)
        logger.debug('Acknowledged scan')
        profiler.trigger()
        await opc_client.send(BotkoOPCUANode.HV_SCAN_ACKNOWLEDGE, False)
        await opc_client.send(BotkoOPCUANode.HV_SCAN_BUSY, True)
        frame: ProfilerFrame = await frame_queue.get()
        await opc_client.send(BotkoOPCUANode.HV_SCAN_BUSY, False)
        await opc_client.send(BotkoOPCUANode.BOTKO_DIAMETER, 0)
        await opc_client.send(BotkoOPCUANode.BOTKO_LENGTH, 0)
        logger.debug('Sending scan finished')
        pcd = frame.get_point_cloud_with_encoder_offsets(encoder_interval=5)
        np.save(f'./data/scan_{datetime.now().strftime("%Y%m%d_%H%M%S")}.npy', pcd)
        free_space_coro = analyzer.get_free_space(pcd, diameter / 2e3)
        free_spaces, show = await asyncio.to_thread(lambda: asyncio.run(free_space_coro))
        await perform_free_space_sequence(
            opc_client,
            BotkoOPCUANode.HV_ENGRAVING_RADIUS,
            BotkoOPCUANode.HV_ENGRAVING_ANGLE,
            BotkoOPCUANode.HV_ENGRAVING_HEIGHT,
            BotkoOPCUANode.HV_SCAN_FINISHED,
            BotkoOPCUANode.HV_START_ROTATION_REQUEST,
            BotkoOPCUANode.HV_ENGRAVING_LOCATION_READY,
            free_spaces,
        )
        passport = analyzer.get_passport_from_ocr(show)
        await send_passport(
            opc_client,
            BotkoOPCUANode.HV_PASSPORT_OWNER_NAME,
            BotkoOPCUANode.HV_PASSPORT_OWNER_CODE,
            BotkoOPCUANode.HV_PASSPORT_SERIAL_NUMBER,
            BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_NAME,
            BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_CODE,
            BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_SERIAL_NUMBER,
            BotkoOPCUANode.HV_PASSPORT_DATE_MANUFACTURING,
            BotkoOPCUANode.HV_PASSPORT_DATE_LAST_TEST,
            BotkoOPCUANode.HV_PASSPORT_TEST_PRESSURE,
            BotkoOPCUANode.HV_PASSPORT_CAPACITY,
            BotkoOPCUANode.HV_PASSPORT_ORIGINAL_TARRA_WEIGHT,
            BotkoOPCUANode.HV_PASSPORT_READY,
            passport,
        )
        await gui.new_measurement(passport, show)
        await profiler.stop_run()
        await opc_client.await_for_value(BotkoOPCUANode.BOTKO_PASSPORT_ACKNOWLEDGE, True)

    except Exception as e:
        logger.error(f'Error during flow iteration: {e}')
        raise


async def main():
    import sys

    from heliovision.camera.keyence.profiler import Profiler
    from heliovision.config import config

    from src.analyzer import Analyzer
    from src.communication.opcua import OPCClient, monitor_faulted
    from src.gui.gui import Gui

    logger.remove()
    logger.add(sys.stderr, level='DEBUG')
    logger.add('./logs/main.log', level='DEBUG')

    logger.info('Starting the main application...')
    profiler = Profiler(config.get_setting('profiler', 'ip'))
    opc_client = OPCClient(
        server_url=config.get_setting('opcua', 'server_url'),
        node_descriptions=BotkoOPCUANode,
    )
    gui = Gui()
    analyzer = Analyzer()
    try:
        frame_queue = await initialize(cast(ProfilerProtocol, profiler), opc_client, gui)
        logger.info('Initialization completed successfully.')
        while True:
            await opc_client.await_for_value(BotkoOPCUANode.BOTKO_FAULTED, False)
            cancel_event = asyncio.Event()
            flow_task = asyncio.create_task(
                flow_iteration(
                    cast(ProfilerProtocol, profiler), frame_queue, opc_client, analyzer, gui
                )
            )
            monitor_task = asyncio.create_task(
                monitor_faulted(opc_client, BotkoOPCUANode.BOTKO_FAULTED, cancel_event)
            )

            done, pending = await asyncio.wait(
                [flow_task, monitor_task], return_when=asyncio.FIRST_COMPLETED
            )
            if cancel_event.is_set():
                logger.warning('Cancel event was set, stopping the flow iteration.')
                try:
                    flow_task.cancel()
                except asyncio.CancelledError:
                    logger.debug('Flow task was cancelled successfully.')
                continue
            if flow_task in done and flow_task.exception():
                logger.error(f'Flow task raised an exception: {flow_task.exception()}')
                await opc_client.send(BotkoOPCUANode.HV_FAULTED, True)
    except Exception as e:
        logger.error(f'An error occurred in the main flow: {e}')
        await opc_client.send(BotkoOPCUANode.HV_FAULTED, True)
    finally:
        logger.info('Shutting down the application...')
        await gui.clear()
        logger.info('Application shutdown complete.')


if __name__ == '__main__':
    asyncio.run(main())

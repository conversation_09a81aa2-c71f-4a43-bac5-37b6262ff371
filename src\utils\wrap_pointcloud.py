import numpy as np


def rotate_profile_around_axis(
    points: np.ndarray,
    theta: float,
    # T_rotax_prof: np.ndarray,
    axis: np.ndarray = np.array([0, 0, 1]),
    origin: np.ndarray = np.array([0, 0, 0]),
) -> np.ndarray:
    """
    Rotate points around a given axis by a specified angle.

    Args:
        points: (N, 3) array of 3D points to be rotated
        theta: rotation angle in radians
        axis: (3,) vector representing the axis of rotation
        origin: (3,) point representing the center of rotation

    Returns:
        rotated_points: (N, 3) array of rotated points
    """
    # Normalize the rotation axis
    axis = axis / np.linalg.norm(axis)

    # Create a rotation matrix using <PERSON><PERSON><PERSON>' rotation formula
    cos_theta = np.cos(theta)
    sin_theta = np.sin(theta)
    ux, uy, uz = axis

    rotation_matrix = np.array(
        [
            [
                cos_theta + ux**2 * (1 - cos_theta),
                ux * uy * (1 - cos_theta) - uz * sin_theta,
                ux * uz * (1 - cos_theta) + uy * sin_theta,
            ],
            [
                uy * ux * (1 - cos_theta) + uz * sin_theta,
                cos_theta + uy**2 * (1 - cos_theta),
                uy * uz * (1 - cos_theta) - ux * sin_theta,
            ],
            [
                uz * ux * (1 - cos_theta) - uy * sin_theta,
                uz * uy * (1 - cos_theta) + ux * sin_theta,
                cos_theta + uz**2 * (1 - cos_theta),
            ],
        ]
    )

    # Translate points to origin, apply rotation, then translate back
    translated_points = points - origin
    rotated_points = rotation_matrix @ translated_points.T
    rotated_points = rotated_points.T + origin

    return rotated_points


def wrap_pointcload(
    points: np.ndarray,
    T_rotax_prof: np.ndarray,
    axis_direction: np.ndarray = np.array([0, 0, 1]),
    axis_point: np.ndarray = np.array([0, 0, 0]),
) -> np.ndarray:
    """
    Wrap points around a cylinder defined by an axis and a point on the axis.

    Args:
        points: (N, 3) array of 3D points to be wrapped
        profile_indices: list of indices for the profiles to be wrapped
        axis_direction: (3,) vector representing the direction of the axis
        axis_point: (3,) point on the axis
    Returns:
        wrapped_points: (N, 3) array of wrapped points
    """
    reconstructed_points = np.zeros_like(points)

    unique_profiles = np.unique(points[:, 1])

    for profile_index in unique_profiles:  # tqdm(unique_profiles):
        # Get points for the current profile
        mask = np.where(points[:, 1] == profile_index)
        if len(mask[0]) == 0:
            continue
        profile_index = profile_index - np.min(unique_profiles)
        profile_index /= np.mean(np.diff(unique_profiles))
        tmp_points = points[mask]
        tmp_points[:, 1] = 0
        points_in_axis_aligned_system = np.ones((tmp_points.shape[0], 4), dtype=np.float32)
        points_in_axis_aligned_system[:, :3] = tmp_points
        points_in_axis_aligned_system = T_rotax_prof @ points_in_axis_aligned_system.T
        profile_points = points_in_axis_aligned_system.T[:, :3]  # Back to 3D

        # Calculate the angle of rotation for the current profile
        theta = 2 * np.pi * (profile_index / len(unique_profiles))

        # Rotate the points around the axis
        rotated_points = rotate_profile_around_axis(
            profile_points, theta, axis_direction, axis_point
        )

        # Store the rotated points in the reconstructed array
        reconstructed_points[mask] = rotated_points
    return reconstructed_points

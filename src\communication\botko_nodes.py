from src.communication.opcua_nodes import OPCUANodes, TypedNode


class BotkoOPCUANode(OPCUANodes):
    """Enum for OPC UA nodes."""

    BOTKO_LIVE_BIT = TypedNode(node_id='ns=5;i=43', data_type=bool)
    BOTKO_FAULTED = TypedNode(node_id='ns=5;i=44', data_type=bool)
    BOTKO_ENGRAVING_BUSY = TypedNode(node_id='ns=5;i=45', data_type=bool)
    BOTKO_UT_HT = TypedNode(node_id='ns=5;i=50', data_type=str)
    BOTKO_DIAMETER = TypedNode(node_id='ns=5;i=51', data_type=int)  # mm
    BOTKO_LENGTH = TypedNode(node_id='ns=5;i=52', data_type=int)  # mm
    BOTKO_SCAN_REQUEST = TypedNode(node_id='ns=5;i=56', data_type=bool)
    BOTKO_PASSPORT_ACKNOWLEDGE = TypedNode(node_id='ns=5;i=46', data_type=bool)

    HV_LIVE_BIT = TypedNode(node_id='ns=5;i=9', data_type=bool)
    HV_FAULTED = TypedNode(node_id='ns=5;i=10', data_type=bool)
    HV_SCAN_ACKNOWLEDGE = TypedNode(node_id='ns=5;i=11', data_type=bool)
    HV_SCAN_BUSY = TypedNode(node_id='ns=5;i=12', data_type=bool)
    HV_SCAN_FINISHED = TypedNode(node_id='ns=5;i=13', data_type=bool)
    HV_START_ROTATION_REQUEST = TypedNode(node_id='ns=5;i=14', data_type=bool)
    HV_ENGRAVING_LOCATION_READY = TypedNode(node_id='ns=5;i=15', data_type=bool)
    HV_PASSPORT_READY = TypedNode(node_id='ns=5;i=16', data_type=bool)

    HV_ENGRAVING_RADIUS = TypedNode(node_id='ns=5;i=20', data_type=int)
    HV_ENGRAVING_HEIGHT = TypedNode(node_id='ns=5;i=21', data_type=int)
    HV_ENGRAVING_ANGLE = TypedNode(node_id='ns=5;i=22', data_type=int)

    HV_PASSPORT_OWNER_NAME = TypedNode(node_id='ns=5;i=26', data_type=str)
    HV_PASSPORT_OWNER_CODE = TypedNode(node_id='ns=5;i=27', data_type=int)
    HV_PASSPORT_SERIAL_NUMBER = TypedNode(node_id='ns=5;i=28', data_type=str)
    HV_PASSPORT_MANUFACTURER_NAME = TypedNode(node_id='ns=5;i=29', data_type=str)
    HV_PASSPORT_MANUFACTURER_CODE = TypedNode(node_id='ns=5;i=30', data_type=int)
    HV_PASSPORT_MANUFACTURER_SERIAL_NUMBER = TypedNode(node_id='ns=5;i=31', data_type=str)
    HV_PASSPORT_DATE_MANUFACTURING = TypedNode(node_id='ns=5;i=32', data_type=str)
    HV_PASSPORT_DATE_LAST_TEST = TypedNode(node_id='ns=5;i=33', data_type=str)
    HV_PASSPORT_TEST_PRESSURE = TypedNode(node_id='ns=5;i=34', data_type=float)
    HV_PASSPORT_CAPACITY = TypedNode(node_id='ns=5;i=35', data_type=float)
    HV_PASSPORT_ORIGINAL_TARRA_WEIGHT = TypedNode(node_id='ns=5;i=36', data_type=float)

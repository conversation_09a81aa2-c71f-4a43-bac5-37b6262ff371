import asyncio
from typing import Optional, Protocol

import numpy as np
from heliovision.gui import Gui as HVGui
from loguru import logger

from src.product.passport import Passport


class GUIProtocol(Protocol):
    async def initialize(self) -> None: ...

    async def new_measurement(self, passport: Passport, image: np.ndarray) -> None: ...

    async def clear(self) -> None: ...


class Gui:
    def __init__(self):
        self._gui = HVGui({'state': 'connecting'})  # type: ignore
        self._bottle_passport: Optional[Passport] = None

        self._gui.broadcast_state()
        self._gui.state['cylinder'] = self

        self._serve_task: Optional[asyncio.Task] = None

    async def initialize(self):
        """Initialize the GUI and start serving."""
        if self._serve_task is None:
            self._serve_task = asyncio.create_task(self._gui.serve('0.0.0.0'))
            logger.debug('Created serve task for GUI.')

    def __json__(self):
        """Return the state of the process as a JSON serializable object."""
        return {
            'state': self._gui.state['state'],
            'passport': self._bottle_passport.model_dump() if self._bottle_passport else None,
        }

    async def new_measurement(self, passport: Passport, image: np.ndarray):
        """Handle a new measurement with the given passport and image."""
        logger.trace(f'New measurement with passport: {passport}')
        self._bottle_passport = passport
        self._gui.update_live_image('ocr_image', image)
        self._gui.broadcast_state()

    async def clear(self):
        """Clear the current state."""
        logger.trace('Clearing GUI state.')
        self._bottle_passport = None
        self._gui.clear_live_image('ocr_image')
        self._gui.broadcast_state()

import numpy as np


def crop_image_to_multiple(image, multiple: int):
    crop_h = image.shape[0] - (image.shape[0] % multiple)
    crop_w = image.shape[1] - (image.shape[1] % multiple)
    return image[:crop_h, :crop_w]


def max_pool_resize(mask, scale_factor):
    """
    Resize mask using maximum pooling to preserve important features.

    Args:
        mask: Input mask array
        scale_factor: Scale factor (e.g., 0.125 for 1/8 size)

    Returns:
        Resized mask using maximum pooling
    """
    assert scale_factor <= 1, f'Scale factor must be at most 1, got {scale_factor}'

    if scale_factor == 1:
        return mask

    kernel_size = int(1 / scale_factor)

    assert kernel_size == 1 / scale_factor, (
        f'Scale factor must be reciprocal of an integer, got {scale_factor}'
    )
    assert mask.shape[0] % kernel_size == 0, (
        f'Mask height must be a multiple of kernel size, got {mask.shape[0]} for kernel size {kernel_size}'
    )
    assert mask.shape[1] % kernel_size == 0, (
        f'Mask width must be a multiple of kernel size, got {mask.shape[1]} for kernel size {kernel_size}'
    )

    # Calculate output dimensions
    h, w = mask.shape[:2]
    new_h = h // kernel_size
    new_w = w // kernel_size

    # Crop to make dimensions divisible by kernel_size
    cropped_mask = mask[: new_h * kernel_size, : new_w * kernel_size]

    # Reshape for max pooling
    reshaped = cropped_mask.reshape(new_h, kernel_size, new_w, kernel_size)

    # Apply max pooling
    pooled = np.max(reshaped, axis=(1, 3))

    return pooled


def min_pool_resize(mask, scale_factor):
    return 1 - max_pool_resize(1 - mask, scale_factor)

import asyncio
from typing import NamedTuple, Protocol

import cv2 as cv
import numba
import numpy as np
import open3d as o3d
from loguru import logger

from src.detections.free_space.free_space import EmptyRectArea, preprocess_profile_image
from src.detections.free_space.lir_basis import widest_interior_rectangle
from src.detections.free_space.model.unet_infer import infer
from src.product.passport import Passport
from src.utils.max_pool import crop_image_to_multiple, min_pool_resize
from src.utils.pointcloud_functions import get_radius_and_theta_at_height
from src.utils.wrap_pointcloud import wrap_pointcload

T_prof_rotax = np.load('./T_prof_rotax_m.npy')
T_rotax_prof = np.linalg.inv(T_prof_rotax)


class Maps(NamedTuple):
    x_map: np.ndarray
    y_map: np.ndarray
    z_map: np.ndarray


@numba.njit
def fill_maps(pcd, x_min, y_min, x_step, y_step, grid_shape):
    x_map = np.zeros(grid_shape, dtype=np.float32)
    y_map = np.zeros(grid_shape, dtype=np.float32)
    z_map = np.ones(grid_shape, dtype=np.float32) * -np.inf
    for i in range(pcd.shape[0]):
        point = pcd[i]
        x_idx = int((point[0] - x_min) / x_step)
        y_idx = int((point[1] - y_min) / y_step)
        if 0 <= x_idx < grid_shape[1] and 0 <= y_idx < grid_shape[0]:
            z_map[y_idx, x_idx] = point[2]
            x_map[y_idx, x_idx] = point[0]
            y_map[y_idx, x_idx] = point[1]
    return x_map, y_map, z_map


def pointcloud_to_maps(pcd: np.ndarray) -> Maps:
    x_min, xmax = pcd[:, 0].min(), pcd[:, 0].max()
    y_min, ymax = pcd[:, 1].min(), pcd[:, 1].max()
    unique_x = np.unique(pcd[:, 0])
    x_step = unique_x[1] - unique_x[0]
    unique_y = np.unique(pcd[:, 1])
    y_step = unique_y[1] - unique_y[0]
    grid_shape = (int((ymax - y_min) / y_step) + 1, int((xmax - x_min) / x_step) + 1)
    x_map, y_map, z_map = fill_maps(pcd, x_min, y_min, x_step, y_step, grid_shape)
    # Delete rows and columns with all -inf values
    valid_rows = np.any(z_map != -np.inf, axis=1)
    valid_cols = np.any(z_map != -np.inf, axis=0)
    z_map = z_map[valid_rows][:, valid_cols]
    x_map = x_map[valid_rows][:, valid_cols]
    y_map = y_map[valid_rows][:, valid_cols]
    # Flip and rotate
    x_map = cv.rotate(x_map, cv.ROTATE_90_COUNTERCLOCKWISE)
    y_map = cv.rotate(y_map, cv.ROTATE_90_COUNTERCLOCKWISE)
    z_map = cv.rotate(z_map, cv.ROTATE_90_COUNTERCLOCKWISE)
    return Maps(
        x_map=x_map,
        y_map=y_map,
        z_map=z_map,
    )


class AnalyzerProtocol(Protocol):
    async def get_free_space(
        self, pointcloud: np.ndarray, radius: float
    ) -> tuple[list[EmptyRectArea], np.ndarray]: ...

    def get_passport_from_ocr(self, image: np.ndarray) -> Passport: ...


class MockAnalyzer(AnalyzerProtocol):
    async def get_free_space(
        self, pointcloud: np.ndarray, radius: float
    ) -> tuple[list[EmptyRectArea], np.ndarray]:
        return [EmptyRectArea(0, 0, 100, 100, 94, 63, 102)], np.zeros((100, 100), dtype=np.uint8)

    def get_passport_from_ocr(self, image: np.ndarray) -> Passport:
        return Passport(
            owner_name='PRAXAIR',
            serial_number='P551865F',
            manufacturer_name='JIN DUNN',
            manufacturer_serial_number='P1183035',
            manufacturing_date='03/2015',
            last_test_date='03/2015',
            test_pressure=300.0,
            capacity=50.7,
            original_tare_weight=57.5,
            wall_thickness=5.2,
        )


class NoFreeSpaceAnalyzer(AnalyzerProtocol):
    async def get_free_space(
        self, pointcloud: np.ndarray, radius: float
    ) -> tuple[list[EmptyRectArea], np.ndarray]:
        pointcloud = np.load('./data/latest_scan/scan_20250623_142612.npy')
        # Convert point cloud to maps
        height = await asyncio.to_thread(input, 'Enter height of the cylinder in m: ')
        height = float(height)
        print(f'Entered height: {height}')

        wrapping_points = pointcloud[::500]
        wrapping_points /= 1000
        wrapped_points = wrap_pointcload(
            wrapping_points,
            T_rotax_prof,
            axis_direction=np.array([0, 0, 1]),
            axis_point=np.array([0, 0, 0]),
        )
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(wrapped_points)
        pcd.estimate_normals()
        normals = np.asarray(pcd.normals)
        test_height = np.percentile(wrapped_points[:, 2], 1)
        radius_at_test, _ = get_radius_and_theta_at_height(
            wrapped_points, normals, float(test_height)
        )
        detected_radius, theta = get_radius_and_theta_at_height(
            wrapped_points, normals, height=height
        )
        detected_radius *= radius / radius_at_test
        detected_radius *= 1000  # Convert to mm
        height *= 1000  # Convert to mm
        print(f'Radius: {detected_radius}, Theta: {theta}')

        rect_area = EmptyRectArea(0, 0, 10, 10, int(detected_radius), int(theta), int(-height))
        logger.info(f'Calculated empty rectangle area: {rect_area}')
        return [rect_area], np.zeros((100, 100), dtype=np.uint8)

    def get_passport_from_ocr(self, image: np.ndarray) -> Passport:
        return Passport(
            owner_name='PRAXAIR',
            serial_number='P551865F',
            manufacturer_name='JIN DUNN',
            manufacturer_serial_number='P1183035',
            manufacturing_date='03/2015',
            last_test_date='03/2015',
            test_pressure=300.0,
            capacity=50.7,
            original_tare_weight=57.5,
            wall_thickness=5.2,
        )


class Analyzer(AnalyzerProtocol):
    def __init__(self):
        self.segment_scale = 4
        self.rect_search_scale = 8

    async def get_free_space(
        self, pointcloud: np.ndarray, radius: float
    ) -> tuple[list[EmptyRectArea], np.ndarray]:
        # Convert point cloud to maps
        maps = await asyncio.to_thread(pointcloud_to_maps, pointcloud)
        depth_map = maps.z_map.copy()
        depth_map *= 1e5  # Scale depth map to match the original scale
        image = preprocess_profile_image(depth_map)
        cv.imwrite('./depth_map.png', image)
        image = crop_image_to_multiple(image, self.segment_scale * self.rect_search_scale)
        image = cv.resize(
            image,
            (0, 0),
            fx=1 / self.segment_scale,
            fy=1 / self.segment_scale,
            interpolation=cv.INTER_NEAREST,
        )
        show = cv.cvtColor(image, cv.COLOR_GRAY2RGB)
        show = cv.resize(show, (0, 0), fx=self.segment_scale, fy=self.segment_scale)
        # show = cv.flip(show, 1)

        # Run inference
        segmentation = infer(image)

        segmentation = cv.erode(
            segmentation, cv.getStructuringElement(cv.MORPH_RECT, (3, 3)), iterations=2
        )
        segmentation = cv.dilate(
            segmentation,
            cv.getStructuringElement(cv.MORPH_RECT, (3, 3)),
            iterations=4,  # a little margin
        )
        binary_mask = segmentation < 0.5
        binary_mask_downscaled = min_pool_resize(binary_mask, 1 / self.rect_search_scale).astype(
            np.bool_
        )
        rect = widest_interior_rectangle(
            binary_mask_downscaled,
            min_width=int(100 / self.rect_search_scale),
            min_height=int(100 / self.rect_search_scale),
            min_area=int(10000 / (self.rect_search_scale**2)),
        )
        rect = rect * self.rect_search_scale
        rect = rect * self.segment_scale
        if rect[2] == 0 and rect[3] == 0:
            logger.warning('No free space found')
            return [], show
        logger.debug(f'Found rectangle: {rect}')
        cv.rectangle(
            show,
            (rect[0], rect[1]),
            (rect[0] + rect[2] - 1, rect[1] + rect[3] - 1),
            (0, 255, 0),
            10,
        )
        # cv.circle(show, (rect[0] + rect[2] - 1, rect[1]), 20, (0, 0, 255), -1)
        cv.imwrite('./show.png', show)

        median_x = np.nanmedian(maps.x_map, axis=1)
        median_z = np.nanmedian(maps.z_map, axis=1)

        median_x_value = median_x[rect[1] + rect[3]] / 1000  # Convert to meters
        median_z_value = median_z[rect[1] + rect[3]] / 1000  # Convert to meters

        point_homog = np.array((median_x_value, 0, median_z_value, 1), dtype=np.float32)
        transformed_point = T_rotax_prof @ point_homog
        height = transformed_point[2]

        wrapping_points = pointcloud[::500]
        wrapping_points /= 1000
        wrapped_points = wrap_pointcload(
            wrapping_points,
            T_rotax_prof,
            axis_direction=np.array([0, 0, 1]),
            axis_point=np.array([0, 0, 0]),
        )
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(wrapped_points)
        pcd.estimate_normals()
        normals = np.asarray(pcd.normals)
        test_height = np.percentile(wrapped_points[:, 2], 1)
        radius_at_test, _ = get_radius_and_theta_at_height(
            wrapped_points, normals, float(test_height)
        )
        detected_radius, theta = get_radius_and_theta_at_height(
            wrapped_points, normals, height=height
        )
        detected_radius *= radius / radius_at_test
        detected_radius *= 1000  # Convert to mm
        height *= 1000  # Convert to mm
        logger.trace(f'Radius: {detected_radius}, Theta: {theta}, Height: {-height}')

        rect = (int(rect[0]), int(rect[1]), int(rect[2]), int(rect[3]))

        rect_area = EmptyRectArea(
            rect[0], rect[1], rect[2], rect[3], int(detected_radius), int(theta), int(-height)
        )
        logger.debug(f'Calculated empty rectangle area: {rect_area}')
        return [rect_area], show

    def get_passport_from_ocr(self, image: np.ndarray) -> Passport:
        return Passport(
            owner_name='PRAXAIR',
            serial_number='P551865F',
            manufacturer_name='JIN DUNN',
            manufacturer_serial_number='P1183035',
            manufacturing_date='03/2015',
            last_test_date='03/2015',
            test_pressure=300.0,
            capacity=50.7,
            original_tare_weight=57.5,
            wall_thickness=5.2,
        )


async def main():
    # Example usage
    analyzer = Analyzer()
    # Load a point cloud from a file or create one
    pcd = np.load('./data/scan_20250623_142151.npy')
    free_space_areas = await analyzer.get_free_space(pcd, 115 / 1e3)
    for area in free_space_areas:
        print(area)


if __name__ == '__main__':
    asyncio.run(main())

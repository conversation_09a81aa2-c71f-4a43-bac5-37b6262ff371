name: Pytest

on:
  pull_request:

jobs:
  test:
    runs-on: ubuntu-latest

    env:
      HV_PYPI_TOKEN: ${{ secrets.HV_PYPI_TOKEN }}

    steps:
    - name: Checkout repository
      uses: actions/checkout@v3

    - name: Set up Python 3.12
      uses: actions/setup-python@v4
      with:
        python-version: '3.12'
    
    - name: Updating PATH
      run: echo "PYTHONPATH=src:." >> $GITHUB_ENV
      shell: bash
    
    - name: Install pipenv
      run: |
        python -m pip install --upgrade pip
        pip install pipenv

    - name: Install dependencies
      run: |
        cd src/
        pipenv --python $(which python)
        pipenv install --dev --skip-lock
        pipenv install torch --index https://download.pytorch.org/whl/cpu --skip-lock
        pipenv lock

    - name: Run tests
      run: |
        cd src/
        pipenv run pytest --tb=short --disable-warnings -q tests/

import re
from datetime import datetime
from enum import Enum
from typing import ClassVar, List

from dateutil.relativedelta import relativedelta
from loguru import logger
from pydantic import BaseModel, Field, field_validator


class RejectionReason(str, Enum):
    """
    Enum representing rejection reasons for bottles (no engravings).
    """

    NIET_KEUREN = 'NIET KEUREN'
    GEEN_KEUR_ENKEL_PAINT = 'GEEN KEUR ENKEL PAINT'
    NO_REJECTION = 'NO REJECTION'
    DEFAULT = 'DEFAULT'


class ApprovalType(str, Enum):
    """
    Enum representing the approval types for bottles.
    """

    HT = 'HT'
    UT = 'UT'
    DEFAULT = ''


class EngravingType(str, Enum):
    """
    Enum representing the possible engraving types for bottles.
    """

    PI = 'PI'
    APRAGAZ_EXPORT = 'APRAGAZ EXPORT'
    APRAGAZ_KEURING = 'APRAGAZ KEURING'
    DEFAULT = ''


class EngravingCode(str, Enum):
    """
    Enum representing the engraving codes for bottles.
    """

    A = 'Â'
    BB = 'BB̂'
    PI_0029_BBNGB_ENCIRCLED = '(π) 0029 BB̂NGB'
    PI_0029_BBNGB = 'π 0029 BB̂NGB'
    PI_0029_B_NGB = '(π) 0029 B NGB'
    UT_PI_0029_B_NGB = 'UT (π) 0029 B NGB'
    DEFAULT = ''


class InputDataExcelTree(BaseModel):
    """
    This class will structure the input data coming from the OCR output.
    """

    drew: bool = Field(default=False)
    wanddikte_treksterkte: bool = Field(default=False)
    hexagon: bool = Field(default=False)
    epsilon: bool = Field(default=False)
    stoomwezen: bool = Field(default=False)
    pi_keur: bool = Field(default=False)
    pi_symbol: bool = Field(default=False)
    e_keur_before_1978: bool = Field(default=False)
    keurdatum_expired: bool = Field(default=False)

    E_KEUR_BOUNDARY: ClassVar[datetime] = datetime(1978, 1, 1)
    KEURDATUM_EXPIRY_TIME: ClassVar[relativedelta] = relativedelta(years=4, months=6)

    @field_validator(
        'drew',
        'wanddikte_treksterkte',
        'hexagon',
        'epsilon',
        'stoomwezen',
        'pi_keur',
        'pi_symbol',
        'e_keur_before_1978',
        'keurdatum_expired',
        mode='before',
    )
    @staticmethod
    def enforce_boolean(value):
        if not isinstance(value, bool):
            raise ValueError(
                f'{value}, must be a boolean value (True/False), instead got type {type(value).__name__}.'
            )
        return value

    @classmethod
    def from_list(cls, input_list: List[str]) -> 'InputDataExcelTree':
        """
        Parses a list of strings into the structured input data model.
        This is used for the excel decision tree.

        Args:
            input_list (List[str]): List of strings to be parsed.

        Returns:
            InputDataExcelTree: The structured data model.
        """
        logger.debug(f'Received input list to be parsed: {input_list}')
        normalized_list = [s.strip().lower() for s in input_list]

        e_keurdatum = cls.extract_date(normalized_list, 'e keur')
        laatste_keurdatum = cls.extract_date(normalized_list, 'keurdatum')

        input_str = ' '.join(normalized_list)

        return cls(
            drew='drew' in input_str,
            wanddikte_treksterkte=any(
                term in input_str for term in ['wanddikte', 'treksterkte']
            ),
            hexagon='hexagon' in input_str,
            epsilon='epsilon' in input_str,
            stoomwezen='stoomwezen' in input_str,
            pi_keur='pi keur' in input_str,
            pi_symbol='π' in input_str,
            e_keur_before_1978=e_keurdatum < cls.E_KEUR_BOUNDARY,
            keurdatum_expired=(laatste_keurdatum + cls.KEURDATUM_EXPIRY_TIME).date()
            < datetime.today().date(),
        )

    @staticmethod
    def extract_date(input_list: List[str], keyword: str) -> datetime:
        """
        Extracts a date (YYYY-MM-DD format) from strings that contain both a keyword and a date.
        If no date is found, raises a ValueError.

        Args:
            input_list (List[str]): List of strings to search in.
            keyword (str): The keyword to search for in the strings.

        Returns:
            datetime: The extracted date. (YYYY-MM-DD format)

        Raises:
            ValueError: If no date is found in the strings.
        """
        date_pattern = r'(\d{4}-\d{2}-\d{2})'

        for entry in input_list:
            entry_lower = entry.lower()
            if keyword in entry_lower:
                match = re.search(date_pattern, entry)
                if match:
                    logger.debug(
                        f'Found date {match.group()} in entry: {entry} for keyword: {keyword}'
                    )
                    return datetime.strptime(match.group(), '%Y-%m-%d')

        raise ValueError(f'Could not find a date for keyword: {keyword}')


class OutputDataExcelTree(BaseModel):
    rejection_status: RejectionReason = Field(default=RejectionReason.DEFAULT)
    approved: bool = Field(default=False)
    approval_type: ApprovalType = Field(default=ApprovalType.DEFAULT)
    engraving_type: EngravingType = Field(default=EngravingType.DEFAULT)
    engraving_code: EngravingCode = Field(default=EngravingCode.DEFAULT)

    @field_validator(
        'rejection_status',
        'approved',
        'approval_type',
        'engraving_type',
        'engraving_code',
        mode='before',
    )
    @staticmethod
    def enforce_valid_output(value):
        if not isinstance(
            value, (RejectionReason, bool, ApprovalType, EngravingType, EngravingCode)
        ):
            raise ValueError(
                f'{value}, must be a valid output data type, instead got type {type(value).__name__}.'
            )
        return value

    @property
    def rejection_reason(self) -> str:
        """
        Returns the rejection reason as a string.

        Returns:
            str: The rejection reason.
        """
        return str(self.rejection_status.value).lower()

    @classmethod
    def from_input_excel_data(cls, input_data: InputDataExcelTree) -> 'OutputDataExcelTree':
        """
        Generates the output data of the excel decsion tree.
        Input should be an instance of InputDataExcelTree.

        Args:
            input_data (InputDataExcelTree): The input data to be processed.

        Returns:
            OutputDataExcelTree: The output data of the decision tree.

        Raises:
            ValueError: If the input data is invalid.
        """
        logger.debug(f'Received input data for excel tree to process: {input_data}')
        if input_data.drew:
            if input_data.keurdatum_expired:
                engraving_type = (
                    EngravingType.PI if input_data.pi_keur else EngravingType.APRAGAZ_EXPORT
                )
                engraving_code = (
                    EngravingCode.PI_0029_BBNGB_ENCIRCLED
                    if input_data.pi_keur
                    else EngravingCode.A
                )
                return cls(
                    rejection_status=RejectionReason.NO_REJECTION,
                    approved=True,
                    approval_type=ApprovalType.HT,
                    engraving_type=engraving_type,
                    engraving_code=engraving_code,
                )
            logger.warning(
                'Drew detected and no expired keurdatum. This Bottle will be rejected!'
            )
            return cls(
                rejection_status=RejectionReason.GEEN_KEUR_ENKEL_PAINT,
                approved=False,
            )

        if input_data.pi_symbol and input_data.wanddikte_treksterkte:
            approval_type = ApprovalType.HT if input_data.e_keur_before_1978 else ApprovalType.UT
            return cls(
                rejection_status=RejectionReason.NO_REJECTION,
                approved=True,
                approval_type=approval_type,
                engraving_type=EngravingType.PI,
                engraving_code=EngravingCode.UT_PI_0029_B_NGB,
            )

        if input_data.pi_symbol:
            return cls(
                rejection_status=RejectionReason.NO_REJECTION,
                approved=True,
                approval_type=ApprovalType.HT,
                engraving_type=EngravingType.PI,
                engraving_code=EngravingCode.PI_0029_BBNGB_ENCIRCLED,
            )

        if input_data.wanddikte_treksterkte:
            raise ValueError(
                f'Invalid input data: {input_data}. PI symbol not detected, but wanddikte_treksterkte is True.'
            )

        if input_data.stoomwezen:
            logger.warning(
                'Stoomwezen detected and no PI and no wanddikte_treksterkte. This Bottle will be rejected!'
            )
            return cls(rejection_status=RejectionReason.NIET_KEUREN, approved=False)

        if not input_data.e_keur_before_1978 and (input_data.hexagon or input_data.epsilon):
            return cls(
                rejection_status=RejectionReason.NO_REJECTION,
                approved=True,
                approval_type=ApprovalType.HT,
                engraving_code=EngravingCode.PI_0029_BBNGB,
            )

        return cls(
            rejection_status=RejectionReason.NO_REJECTION,
            approved=True,
            approval_type=ApprovalType.HT,
            engraving_type=EngravingType.APRAGAZ_KEURING,
            engraving_code=EngravingCode.BB,
        )

    def to_engraving_string(self) -> str:
        """
        Generates the engraving output string for the machine.
        If the bottle has been rejected, returns an empty string.

        Returns:
            str: The engraving string
        """
        if self.rejection_status == RejectionReason.DEFAULT:
            logger.warning(
                'No input data was provided yet! No engraving string can be generated.'
            )
            return ''
        if self.rejection_status != RejectionReason.NO_REJECTION:
            logger.warning(
                f'Cannot generate engraving string for a rejected bottle: {self.rejection_status}!'
            )
            return ''

        engraving_components = [
            self.engraving_type.value if self.engraving_type else '',
            self.engraving_code.value if self.engraving_code else '',
            datetime.today().strftime('%Y/%m'),
        ]

        engraving_string = ' '.join(filter(None, engraving_components))
        logger.info(f'Generated engraving string: {engraving_string}')

        return engraving_string

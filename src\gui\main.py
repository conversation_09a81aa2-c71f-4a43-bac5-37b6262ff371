import asyncio

import numpy as np
from loguru import logger

from src.gui.gui import Gui
from src.product.passport import Passport


async def send_passport(gui: Gui):
    """Send the passport data to the GUI."""
    passport = Passport(
        barcode='1234567890123',
        diameter=100,
        length=200,
        owner_name='PRAXAIR',
        serial_number='P551865F',
        manufacturer_name='JIN DUNN',
        manufacturer_serial_number='P1183035',
        manufacturing_date='03/2015',
        last_test_date='03/2015',
        test_pressure=300.0,
        capacity=50.7,
        original_tare_weight=57.5,
        wall_thickness=5.2,
    )
    img = np.zeros((100, 100), dtype=np.uint8)  # Dummy image for testing
    await gui.new_measurement(passport, img)  # Assuming image is not needed for this test


async def main():
    gui = Gui()
    tasks = set()
    serve_task = asyncio.create_task(gui._gui.serve())
    send_task = asyncio.create_task(send_passport(gui))
    tasks.add(serve_task)
    tasks.add(send_task)
    await asyncio.gather(*tasks)

    await asyncio.Future()  # Keep the event loop running


if __name__ == '__main__':
    logger.info('Starting GUI application...')
    asyncio.run(main())
    logger.info('GUI application has stopped.')

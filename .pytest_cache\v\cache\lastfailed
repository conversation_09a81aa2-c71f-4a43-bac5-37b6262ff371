{"src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_OWNER_CODE-6-12345]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_SERIAL_NUMBER-12-SN123456]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_CODE-6-54321]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_SERIAL_NUMBER-12-MSN123456]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_DATE_LAST_TEST-12-2023-06-01]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_TEST_PRESSURE-10-200.0]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_ORIGINAL_TARRA_WEIGHT-10-10.0]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.BOTKO_FAULTED-True]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.BOTKO_UT_HT-HT]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.BOTKO_DIAMETER-100]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.BOTKO_SCAN_REQUEST-True]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_FAULTED-True]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_SCAN_BUSY-True]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_SCAN_FINISHED-True]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_ENGRAVING_LOCATION_READY-True]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_PASSPORT_READY-True]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_ENGRAVING_HEIGHT-100]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_ENGRAVING_ANGLE-90]": true, "src/tests/test_lir_basis.py::test_lir_basic_rectangle": true, "src/tests/test_lir_basis.py::test_lir_empty_grid": true, "src/tests/test_lir_basis.py::test_lir_full_grid": true, "src/tests/test_lir_basis.py::test_lir_with_constraints": true, "src/tests/test_lir_basis.py::test_lir_l_shape": true, "src/tests/test_lir_basis.py::test_lir_single_pixel": true, "src/tests/test_lir_basis.py::test_lir_min_area_constraint": true, "src/tests/test_lir_basis.py::test_lir_min_height_constraint": true, "src/tests/test_lir_basis.py::test_lir_complex_shape": true, "src/tests/test_main_flow.py::test_initialize": true, "src/tests/test_main_flow.py::test_main_flow_iteration": true, "src/tests/test_preferred_position.py::test_sort_1_element": true, "src/tests/test_preferred_position.py::test_sort_5_elements_with_max_candidates": true, "src/tests/test_preferred_position.py::test_sort_elements_same_size": true, "src/tests/test_preferred_position.py::test_sort_elements_different_sizes": true, "src/tests/test_unet_infer.py::test_unet_infer_basic": true, "src/tests/test_unet_infer.py::test_unet_infer_different_sizes": true, "src/tests/test_unet_infer.py::test_unet_infer_edge_cases": true, "src/tests/test_unet_infer.py::test_unet_infer_deterministic": true, "src/tests/test_unet_infer.py::test_unet_infer_output_type": true, "src/tests/test_main_flow.py": true}
name: Pyright Check

on: pull_request

jobs:
  pyright-check:
    runs-on: ubuntu-latest

    env:
      # --- metadata ---
      _HV_CI_PYRIGHT_VERSION: 0.1.1
      _HV_CI_PYRIGHT_IS_CUSTOM: "No"
      
      # --- CI project parameters ---
      PIPENV_PIPFILE: src/Pipfile
      PYTHONPATH: src:.
      PYTHON_WORKDIR: .
      PYTHON_VERSION: "3.12"
      
      # --- CI static parameters ---
      HV_PYPI_TOKEN: ${{ secrets.HV_PYPI_TOKEN }}

    steps:
    - name: Log metadata
      run: |
        echo "CI PyRight version: ${{ env._HV_CI_PYRIGHT_VERSION }}"
        echo "CI PyRight is custom: ${{ env._HV_CI_PYRIGHT_IS_CUSTOM }}" 

    - name: Checkout repository
      uses: actions/checkout@v3

    - name: Set up Python ${{ env.PYTHON_VERSION }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pipenv'  # caching pipenv dependencies
    
    - name: Install pipenv
      run: |
        python -m pip install --upgrade pip
        pip install pipenv

    - name: Install dependencies
      run: |
        pipenv --python $(which python)
        pipenv sync
        echo "$(pipenv --venv)/bin" >> $GITHUB_PATH  # add pipenv to PATH for pyright

    - uses: jakebailey/pyright-action@v2
      with:
        version: 1.1.398

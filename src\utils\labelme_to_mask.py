import json

import cv2 as cv
import numpy as np


def parse_labelme_to_mask(
    json_path: str,
    unknown_label=0,
    clean_label=1,
    text_label=2,
) -> np.ndarray:
    """
    Parse LabelMe JSON polygons to a mask with training labels.

    Args:
        json_path: Path to LabelMe JSON file,
                   only polygons with labels "clean" and "text" are considered.

    Returns:
        np.ndarray: Mask with training labels:
            0 = unknown/background
            1 = clean areas
            2 = text areas
    """
    # Load JSON data
    with open(json_path, 'r') as f:
        json_data = json.load(f)

    # Get image dimensions from JSON metadata
    img_height = json_data.get('imageHeight')
    img_width = json_data.get('imageWidth')

    if img_height is None or img_width is None:
        raise ValueError(f'imageHeight and imageWidth must be present in JSON file: {json_path}')

    # Initialize mask with zeros (unknown/background)
    mask = np.zeros((img_height, img_width), dtype=np.uint8)
    mask[:] = unknown_label

    # Process each shape
    shapes = json_data.get('shapes', [])

    for shape in shapes:
        if shape.get('shape_type') != 'polygon':
            continue

        label = shape.get('label', '').lower()
        if label not in ('clean', 'text'):
            print(f'Label {label} not supported, skipping {json_path}')
            continue

        points = shape.get('points', [])

        if not points:
            continue

        # Convert points to numpy array and ensure integer coordinates
        polygon_points = np.array(points, dtype=np.int32)

        # Create temporary mask for this polygon
        temp_mask = np.zeros((img_height, img_width), dtype=np.uint8)
        cv.fillPoly(temp_mask, [polygon_points], (255,))

        # Assign training label based on shape label
        if label == 'clean':
            # Set clean areas to 1, but don't overwrite text areas (2)
            mask[temp_mask == 255] = clean_label
        elif label == 'text':
            # Set text areas to 2 (this will overwrite clean areas if they overlap)
            mask[temp_mask == 255] = text_label

    return mask


def visualize_mask(mask: np.ndarray, title: str = 'Training Labels Mask'):
    """
    Visualize the training labels mask with different colors.

    Args:
        mask: Training labels mask
        title: Plot title
    """
    try:
        import matplotlib.pyplot as plt
        from matplotlib.patches import Patch
    except ImportError:
        print('matplotlib not available for visualization')
        return

    # Create color map: 0=black, 1=green (clean), 2=red (text)
    colored_mask = np.zeros((*mask.shape, 3), dtype=np.uint8)
    colored_mask[mask == 1] = [0, 255, 0]  # Green for clean
    colored_mask[mask == 2] = [255, 0, 0]  # Red for text

    plt.figure(figsize=(12, 8))
    plt.imshow(colored_mask)
    plt.title(title)
    plt.axis('off')

    # Add legend
    legend_elements = [
        Patch(facecolor='black', label='Unknown (0)'),
        Patch(facecolor='green', label='Clean (1)'),
        Patch(facecolor='red', label='Text (2)'),
    ]
    plt.legend(handles=legend_elements, loc='upper right')
    plt.show()

import numpy as np

from src.detections.free_space.lir_basis import largest_interior_rectangle


def test_lir_basic_rectangle():
    """Test LIR with a simple rectangular grid."""
    # Create a 5x5 grid with a 3x3 True rectangle in the center
    grid = np.array(
        [
            [False, False, False, False, False],
            [False, True, True, True, False],
            [False, True, True, True, False],
            [False, True, True, True, False],
            [False, False, False, False, False],
        ]
    )

    result = largest_interior_rectangle(grid)

    # Should return [x, y, width, height]
    assert len(result) == 4  # x, y, width, height
    assert result[2] == 3 and result[3] == 3  # width=3, height=3


def test_lir_empty_grid():
    """Test LIR with empty grid."""
    grid = np.array([[False, False, False], [<PERSON>alse, <PERSON>alse, False], [<PERSON>alse, <PERSON>alse, <PERSON>alse]])

    result = largest_interior_rectangle(grid)
    # Should return [0, 0, 0, 0] for empty grid
    assert result[2] == 0 and result[3] == 0  # width=0, height=0


def test_lir_full_grid():
    """Test LIR with completely filled grid."""
    grid = np.array([[True, True, True], [True, True, True], [True, True, True]])

    result = largest_interior_rectangle(grid)
    # Should return [x, y, 3, 3] for full 3x3 grid
    assert result[2] == 3 and result[3] == 3  # width=3, height=3


def test_lir_with_constraints():
    """Test LIR with minimum width/height constraints."""
    grid = np.array([[True, True, False], [True, True, False], [False, False, False]])

    # Without constraints, should find 2x2
    result_no_constraint = largest_interior_rectangle(grid)
    assert result_no_constraint[2] == 2 and result_no_constraint[3] == 2  # width=2, height=2

    # With min_width=3, should find nothing
    result_with_constraint = largest_interior_rectangle(grid, min_width=3)
    assert result_with_constraint[2] == 0 and result_with_constraint[3] == 0  # width=0, height=0


def test_lir_l_shape():
    """Test LIR with L-shaped region."""
    grid = np.array(
        [
            [True, True, False, False],
            [True, True, False, False],
            [True, True, True, True],
            [True, True, True, True],
        ]
    )

    result = largest_interior_rectangle(grid)
    # Should find either 2x4 or 4x2 rectangle
    area = result[2] * result[3]  # width * height
    assert area == 8  # Maximum possible area


def test_lir_single_pixel():
    """Test LIR with single True pixel."""
    grid = np.array([[False, False, False], [False, True, False], [False, False, False]])

    result = largest_interior_rectangle(grid)
    # Should find 1x1 rectangle
    assert result[2] == 1 and result[3] == 1  # width=1, height=1
    assert result[0] == 1 and result[1] == 1  # x=1, y=1


def test_lir_min_area_constraint():
    """Test LIR with minimum area constraint."""
    grid = np.array([[True, True, False], [True, True, False], [False, False, True]])

    # Without area constraint, should find 2x2 rectangle (area=4)
    result_no_constraint = largest_interior_rectangle(grid)
    assert result_no_constraint[2] * result_no_constraint[3] == 4

    # With min_area=5, should find nothing
    result_with_constraint = largest_interior_rectangle(grid, min_area=5)
    assert result_with_constraint[2] == 0 and result_with_constraint[3] == 0


def test_lir_min_height_constraint():
    """Test LIR with minimum height constraint."""
    # Create a 4x2 rectangle (wide but short)
    grid = np.array(
        [[True, True, True, True], [True, True, True, True], [False, False, False, False]]
    )

    # Without constraints, should find 4x2
    result_no_constraint = largest_interior_rectangle(grid)
    assert result_no_constraint[2] == 4 and result_no_constraint[3] == 2

    # With min_height=3, should find nothing
    result_with_constraint = largest_interior_rectangle(grid, min_height=3)
    assert result_with_constraint[2] == 0 and result_with_constraint[3] == 0


def test_lir_complex_shape():
    """Test LIR with a more complex shape."""
    grid = np.array(
        [
            [True, True, True, False, False],
            [True, True, True, False, False],
            [True, True, True, True, True],
            [False, False, True, True, True],
            [False, False, True, True, True],
        ]
    )

    result = largest_interior_rectangle(grid)
    # Should find a rectangle with area >= 6 (multiple possibilities)
    area = result[2] * result[3]
    assert area >= 6

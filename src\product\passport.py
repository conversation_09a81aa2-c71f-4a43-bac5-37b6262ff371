from enum import Enum
from typing import Optional

from pydantic import BaseModel, Field


class TestType(str, Enum):
    """Enum for test types."""

    UT = 'UT'
    HT = 'HT'
    None_ = None


class Engraving(str, Enum):
    """Enum for engraving types."""

    APRAGAZ = 'APRAGAZ'
    APRAGAZ_EXPORT = 'APRAGAZ EXPORT'
    PI = 'PI'
    None_ = None


class Passport(BaseModel):
    barcode: Optional[str] = Field(
        default=None, description='The barcode on the passport, linked to gas bottle.'
    )
    diameter: Optional[int] = Field(default=None, description='Diameter of the gas bottle.')
    length: Optional[int] = Field(default=None, description='Length of the gas bottle.')
    owner_name: Optional[str] = Field(
        default=None, description='Name of the owner of the gas bottle.'
    )
    owner_code: Optional[int] = Field(
        default=0, description='Code of the owner of the gas bottle.'
    )
    serial_number: Optional[str] = Field(
        default=None, description='Serial number of the owner of the gas bottle.'
    )
    manufacturer_name: Optional[str] = Field(
        default=None, description='Name of the manufacturer of the gas bottle.'
    )
    manufacturer_code: Optional[int] = Field(
        default=0, description='Code of the manufacturer of the gas bottle.'
    )
    manufacturer_serial_number: Optional[str] = Field(
        default=None,
        description='Serial number of the manufacturer of the gas bottle.',
    )
    manufacturing_date: Optional[str] = Field(
        default=None, description='Manufacturing date of the gas bottle.'
    )
    last_test_date: Optional[str] = Field(
        default=None, description='Last test date of the gas bottle.'
    )
    test_pressure: Optional[float] = Field(
        default=None, description='Test pressure of the gas bottle.'
    )
    capacity: Optional[float] = Field(default=None, description='Capacity of the gas bottle.')
    original_tare_weight: Optional[float] = Field(
        default=None, description='Original tare weight of the gas bottle.'
    )
    wall_thickness: Optional[float] = Field(
        default=None, description='Wall thickness of the gas bottle.'
    )
    ut_ht: Optional[TestType] = Field(default=None, description='Test type.')
    engraving: Optional[Engraving] = Field(default=None, description='Engraving type.')

<script lang="ts">
    import {Image, Spinner} from "@sveltestrap/sveltestrap";
    import { gui } from "../stores.js";

    const liveImages = gui.liveImages;
</script>

<div style="width: 100%; height: 100%; display: flex; justify-content: center; align-items: center;">
     {#if $liveImages.ocr_image !== undefined}
        <Image src={$liveImages.ocr_image} style="width: 100%; height: 100%; border-radius: 0;" />
    {:else}
        <Spinner color="primary" />
    {/if}
</div>

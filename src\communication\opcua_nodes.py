from enum import Enum
from typing import Any, NamedTuple, Type


class TypedNode(NamedTuple):
    """NamedTuple for OPC UA nodes."""

    node_id: str
    data_type: Type[Any]


class OPCUANodes(Enum):
    """Base class for OPC UA nodes."""

    @classmethod
    def get_node(cls, name: str) -> TypedNode:
        """Get a node by its name."""
        if name not in cls.__members__:
            raise ValueError(f'Node {name} not found')
        node = cls.__members__[name].value
        if not isinstance(node, TypedNode):
            raise ValueError(f'Node {name} is not a TypedNode')
        return node

    @classmethod
    def get_all_nodes(cls) -> dict[str, TypedNode]:
        """Get all nodes."""
        return {name: node.value for name, node in cls.__members__.items()}

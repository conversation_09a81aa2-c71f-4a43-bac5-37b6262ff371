import json
import os
import tempfile

import numpy as np
import pytest

from src.utils.labelme_to_mask import parse_labelme_to_mask


class TestLabelmeMask:
    """Test cases for labelme_to_mask function."""

    def create_test_labelme_json(self, shapes, width=100, height=100):
        """Helper to create test LabelMe JSON data."""
        return {'imageWidth': width, 'imageHeight': height, 'shapes': shapes}

    def test_labelme_basic_polygon(self):
        """Test basic polygon parsing."""
        # Create a simple square polygon labeled as 'clean'
        shapes = [
            {
                'label': 'clean',
                'points': [[10, 10], [40, 10], [40, 40], [10, 40]],
                'shape_type': 'polygon',
            }
        ]

        json_data = self.create_test_labelme_json(shapes)

        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(json_data, f)
            temp_path = f.name

        try:
            mask = parse_labelme_to_mask(temp_path)

            # Check mask properties
            assert mask.shape == (100, 100)
            assert mask.dtype == np.uint8

            # Check that clean area is labeled as 1
            assert mask[20, 20] == 1  # Inside the polygon
            assert mask[5, 5] == 0  # Outside the polygon

        finally:
            os.unlink(temp_path)

    def test_labelme_overlapping_polygons(self):
        """Test overlapping clean and text polygons."""
        shapes = [
            {
                'label': 'clean',
                'points': [[10, 10], [50, 10], [50, 50], [10, 50]],
                'shape_type': 'polygon',
            },
            {
                'label': 'text',
                'points': [[30, 30], [70, 30], [70, 70], [30, 70]],
                'shape_type': 'polygon',
            },
        ]

        json_data = self.create_test_labelme_json(shapes)

        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(json_data, f)
            temp_path = f.name

        try:
            mask = parse_labelme_to_mask(temp_path)

            # Check different regions
            assert mask[20, 20] == 1  # Clean only area
            assert mask[60, 60] == 2  # Text only area
            assert mask[40, 40] == 2  # Overlapping area (text should override)
            assert mask[5, 5] == 0  # Background

        finally:
            os.unlink(temp_path)

    def test_labelme_custom_labels(self):
        """Test custom label mapping."""
        shapes = [
            {
                'label': 'clean',
                'points': [[10, 10], [30, 10], [30, 30], [10, 30]],
                'shape_type': 'polygon',
            }
        ]

        json_data = self.create_test_labelme_json(shapes)

        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(json_data, f)
            temp_path = f.name

        try:
            mask = parse_labelme_to_mask(
                temp_path, unknown_label=100, clean_label=200, text_label=255
            )

            assert mask[20, 20] == 200  # Clean area with custom label
            assert mask[5, 5] == 100  # Background with custom label

        finally:
            os.unlink(temp_path)

    def test_labelme_missing_dimensions(self):
        """Test error handling for missing image dimensions."""
        shapes = []
        json_data = {'shapes': shapes}  # Missing imageWidth and imageHeight

        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(json_data, f)
            temp_path = f.name

        try:
            with pytest.raises(ValueError, match='imageHeight and imageWidth must be present'):
                parse_labelme_to_mask(temp_path)
        finally:
            os.unlink(temp_path)

    def test_labelme_empty_shapes(self):
        """Test parsing with no shapes."""
        shapes = []
        json_data = self.create_test_labelme_json(shapes, 50, 50)

        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(json_data, f)
            temp_path = f.name

        try:
            mask = parse_labelme_to_mask(temp_path)

            # Should be all zeros (unknown/background)
            assert mask.shape == (50, 50)
            assert np.all(mask == 0)

        finally:
            os.unlink(temp_path)

    def test_labelme_unknown_label(self):
        """Test parsing with unknown label (should be ignored)."""
        shapes = [
            {
                'label': 'unknown_label',
                'points': [[10, 10], [30, 10], [30, 30], [10, 30]],
                'shape_type': 'polygon',
            },
            {
                'label': 'clean',
                'points': [[40, 40], [60, 40], [60, 60], [40, 60]],
                'shape_type': 'polygon',
            },
        ]

        json_data = self.create_test_labelme_json(shapes)

        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(json_data, f)
            temp_path = f.name

        try:
            mask = parse_labelme_to_mask(temp_path)

            # Unknown label should be ignored, only clean should be labeled
            assert mask[20, 20] == 0  # Unknown label area should remain 0
            assert mask[50, 50] == 1  # Clean area should be 1

        finally:
            os.unlink(temp_path)

    def test_labelme_triangle_polygon(self):
        """Test parsing with triangular polygon."""
        shapes = [
            {
                'label': 'text',
                'points': [[25, 10], [40, 40], [10, 40]],  # Triangle
                'shape_type': 'polygon',
            }
        ]

        json_data = self.create_test_labelme_json(shapes, 50, 50)

        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(json_data, f)
            temp_path = f.name

        try:
            mask = parse_labelme_to_mask(temp_path)

            # Check that triangle is properly filled
            assert mask[30, 25] == 2  # Inside triangle
            assert mask[10, 10] == 0  # Outside triangle
            assert mask[40, 40] == 2  # On triangle vertex

        finally:
            os.unlink(temp_path)

    def test_labelme_multiple_same_label(self):
        """Test parsing with multiple polygons of same label."""
        shapes = [
            {
                'label': 'clean',
                'points': [[10, 10], [30, 10], [30, 30], [10, 30]],
                'shape_type': 'polygon',
            },
            {
                'label': 'clean',
                'points': [[40, 40], [60, 40], [60, 60], [40, 60]],
                'shape_type': 'polygon',
            },
        ]

        json_data = self.create_test_labelme_json(shapes, 70, 70)

        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(json_data, f)
            temp_path = f.name

        try:
            mask = parse_labelme_to_mask(temp_path)

            # Both areas should be labeled as clean
            assert mask[20, 20] == 1  # First clean area
            assert mask[50, 50] == 1  # Second clean area
            assert mask[35, 35] == 0  # Between the areas

        finally:
            os.unlink(temp_path)

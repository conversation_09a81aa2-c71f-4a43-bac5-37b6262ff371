import numpy as np
import pytest

from src.utils.max_pool import max_pool_resize


def test_max_pool_basic():
    """Test basic max pooling operation."""
    # Create a 4x4 mask with known pattern
    mask = np.array([[0, 1, 0, 0], [0, 0, 0, 1], [1, 0, 0, 0], [0, 0, 1, 0]])

    # Pool with scale factor 0.5 (2x2 -> 1x1 pooling)
    result = max_pool_resize(mask, 0.5)

    # Should be 2x2 output
    assert result.shape == (2, 2)

    # Check max pooling results
    assert result[0, 0] == 1  # max(0,1,0,0) = 1
    assert result[0, 1] == 1  # max(0,0,0,1) = 1
    assert result[1, 0] == 1  # max(1,0,0,0) = 1
    assert result[1, 1] == 1  # max(0,0,1,0) = 1


def test_max_pool_quarter_scale():
    """Test max pooling with quarter scale."""
    # Create an 8x8 mask
    mask = np.zeros((8, 8))
    mask[1, 1] = 1  # Single pixel in first quadrant
    mask[3, 6] = 1  # Single pixel in second quadrant
    mask[6, 2] = 1  # Single pixel in third quadrant
    mask[7, 7] = 1  # Single pixel in fourth quadrant

    # Pool with scale factor 0.25 (4x4 -> 1x1 pooling)
    result = max_pool_resize(mask, 0.25)

    # Should be 2x2 output
    assert result.shape == (2, 2)
    assert result[0, 0] == 1  # Contains the pixel at (1,1)
    assert result[0, 1] == 1  # Contains the pixel at (3,6)
    assert result[1, 0] == 1  # Contains the pixel at (6,2)
    assert result[1, 1] == 1  # Contains the pixel at (7,7)


def test_max_pool_no_scaling():
    """Test max pooling with scale factor 1.0 (no change)."""
    mask = np.array([[1, 0, 1], [0, 1, 0], [1, 0, 1]])

    result = max_pool_resize(mask, 1.0)

    # Should be identical to input
    assert np.array_equal(result, mask)


def test_max_pool_invalid_scale():
    """Test error handling for invalid scale factors."""
    mask = np.array([[1, 0], [0, 1]])

    # Scale factor > 1 should raise assertion error
    with pytest.raises(AssertionError, match='Scale factor must be at most 1'):
        max_pool_resize(mask, 1.5)


def test_max_pool_non_integer_reciprocal():
    """Test error handling for non-integer reciprocal scale factors."""
    mask = np.array([[1, 0, 1], [0, 1, 0], [1, 0, 1]])

    # Scale factor that doesn't have integer reciprocal
    with pytest.raises(AssertionError, match='Scale factor must be reciprocal of an integer'):
        max_pool_resize(mask, 0.3)


def test_max_pool_dimension_mismatch():
    """Test error handling for dimension mismatches."""
    # Create 5x5 mask (not divisible by 2)
    mask = np.ones((5, 5))

    # Scale factor 0.5 requires dimensions divisible by 2
    with pytest.raises(AssertionError, match='Mask height must be a multiple of kernel size'):
        max_pool_resize(mask, 0.5)


def test_max_pool_preserves_max_values():
    """Test that max pooling preserves maximum values in each region."""
    # Create mask with different values in each 2x2 region
    mask = np.array(
        [
            [0.1, 0.9, 0.2, 0.8],
            [0.3, 0.7, 0.4, 0.6],
            [0.5, 0.5, 0.1, 0.9],
            [0.2, 0.8, 0.3, 0.7],
        ]
    )

    result = max_pool_resize(mask, 0.5)

    # Check that maximum values are preserved
    assert result[0, 0] == 0.9  # max of top-left 2x2
    assert result[0, 1] == 0.8  # max of top-right 2x2
    assert result[1, 0] == 0.8  # max of bottom-left 2x2
    assert result[1, 1] == 0.9  # max of bottom-right 2x2


def test_max_pool_eighth_scale():
    """Test max pooling with 1/8 scale factor."""
    # Create a 16x16 mask
    mask = np.zeros((16, 16))
    # Place values in different 8x8 regions
    mask[2, 2] = 0.5
    mask[10, 10] = 0.8
    mask[3, 12] = 0.3
    mask[14, 5] = 0.7

    # Pool with scale factor 0.125 (8x8 -> 1x1 pooling)
    result = max_pool_resize(mask, 0.125)

    # Should be 2x2 output
    assert result.shape == (2, 2)
    assert result[0, 0] == 0.5  # Contains the pixel at (2,2)
    assert result[0, 1] == 0.3  # Contains the pixel at (3,12)
    assert result[1, 0] == 0.7  # Contains the pixel at (14,5)
    assert result[1, 1] == 0.8  # Contains the pixel at (10,10)


def test_max_pool_all_zeros():
    """Test max pooling with all zero input."""
    mask = np.zeros((4, 4))

    result = max_pool_resize(mask, 0.5)

    # Should be all zeros
    assert result.shape == (2, 2)
    assert np.all(result == 0)


def test_max_pool_all_ones():
    """Test max pooling with all ones input."""
    mask = np.ones((6, 6))

    result = max_pool_resize(mask, 1 / 3)  # 3x3 -> 1x1 pooling

    # Should be all ones
    assert result.shape == (2, 2)
    assert np.all(result == 1)


def test_max_pool_negative_values():
    """Test max pooling with negative values."""
    mask = np.array(
        [
            [-0.5, -0.1, -0.8, -0.2],
            [-0.3, -0.9, -0.4, -0.6],
            [-0.7, -0.2, -0.1, -0.9],
            [-0.1, -0.8, -0.3, -0.5],
        ]
    )

    result = max_pool_resize(mask, 0.5)

    # Should preserve maximum (least negative) values
    assert result[0, 0] == -0.1  # max(-0.5, -0.1, -0.3, -0.9)
    assert result[0, 1] == -0.2  # max(-0.8, -0.2, -0.4, -0.6)
    assert result[1, 0] == -0.1  # max(-0.7, -0.2, -0.1, -0.8)
    assert result[1, 1] == -0.1  # max(-0.1, -0.9, -0.3, -0.5)


def test_max_pool_mixed_values():
    """Test max pooling with mixed positive and negative values."""
    mask = np.array(
        [
            [-0.5, 0.8, -0.2, 0.3],
            [0.1, -0.9, 0.7, -0.4],
            [0.6, -0.1, -0.8, 0.9],
            [-0.3, 0.4, 0.2, -0.7],
        ]
    )

    result = max_pool_resize(mask, 0.5)

    # Should preserve maximum values (positive over negative)
    assert result[0, 0] == 0.8  # max(-0.5, 0.8, 0.1, -0.9)
    assert result[0, 1] == 0.7  # max(-0.2, 0.3, 0.7, -0.4)
    assert result[1, 0] == 0.6  # max(0.6, -0.1, -0.3, 0.4)
    assert result[1, 1] == 0.9  # max(-0.8, 0.9, 0.2, -0.7)

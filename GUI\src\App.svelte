<script lang="ts">
  // Imports
  import { BottlePage } from "./lib";
  import { Spinner } from "@sveltestrap/sveltestrap";
  import { FRow } from "@heliovision/sveltestrap";
  import { gui } from "./stores.js";
  import { PythonLayout } from "@heliovision/gui";

  const state = gui.state;

  // Reactive variables
  $: isConnected = !$state.connecting;
</script>

<PythonLayout project="Nippon Graveer" logo="heliovision" {gui}>
  {#if isConnected}
    <BottlePage/>
  {:else}
    <div class="d-flex justify-content-center flex-column" style="height: 70vh;">
      <FRow vCenter>
        <Spinner /> <h1 class="mx-4"> Connecting to system...</h1>
      </FRow>
    </div>
  {/if}
</PythonLayout>
